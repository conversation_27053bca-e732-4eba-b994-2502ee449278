#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证最新生成文件中的编码格式修复
"""

import pandas as pd

def verify_encoding_fix():
    """验证编码格式修复"""
    print("🔍 验证最新生成文件中的编码格式修复")
    print("=" * 50)
    
    try:
        # 读取最新生成的Excel文件
        import os
        output_dir = 'output'
        files = [f for f in os.listdir(output_dir) if f.startswith('完整检查项目清单_') and f.endswith('.xlsx')]
        if not files:
            print("❌ 未找到输出文件")
            return False

        # 按修改时间排序，获取最新文件
        files_with_time = [(f, os.path.getmtime(os.path.join(output_dir, f))) for f in files]
        latest_file = max(files_with_time, key=lambda x: x[1])[0]
        file_path = os.path.join(output_dir, latest_file)
        print(f"📁 读取文件: {latest_file}")
        df = pd.read_excel(file_path, sheet_name='医保项目')
        
        # 筛选使用关联部位的项目
        related_projects = df[df['使用关联部位'] == '是']
        
        print(f"📊 总项目数: {len(df)}")
        print(f"📊 关联部位项目数: {len(related_projects)}")
        print("\n🔍 关联部位项目编码格式验证:")
        
        for idx, row in related_projects.iterrows():
            code = row['关联部位编码']
            code_type = type(code).__name__
            is_string = isinstance(code, str)
            is_digit = str(code).isdigit() if code else False
            
            print(f"  - {row['检查项目名称'][:40]}...")
            print(f"    关联部位: {row['关联部位']}")
            print(f"    关联部位编码: {code} (类型: {code_type})")
            print(f"    是否字符串: {is_string}, 是否纯数字: {is_digit}")
            
            if is_string and is_digit:
                print(f"    ✅ 编码格式正确")
            else:
                print(f"    ❌ 编码格式异常")
            print()
        
        # 检查是否还有浮点格式的编码
        float_codes = []
        for idx, row in df.iterrows():
            code = row['关联部位编码']
            if code and '.' in str(code):
                float_codes.append((row['检查项目名称'], code))
        
        if float_codes:
            print(f"⚠️  发现 {len(float_codes)} 个仍为浮点格式的编码:")
            for name, code in float_codes:
                print(f"  - {name}: {code}")
        else:
            print("✅ 所有编码均已修复为字符串格式")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    verify_encoding_fix()