#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# 读取医保项目数据
df = pd.read_excel('output/完整检查项目清单_20250711_230350.xlsx', sheet_name='医保项目')

print('医保项目总数:', len(df))
print('使用关联部位的项目数量:', len(df[df['使用关联部位'] == '是']))
print('使用默认部位的项目数量:', len(df[df['使用关联部位'] == '否']))

print('\n使用关联部位的项目:')
associated_projects = df[df['使用关联部位'] == '是']
if len(associated_projects) > 0:
    print(associated_projects[['检查项目名称', '关联部位', '关联部位编码', '部位编码']].head(10))
else:
    print('没有使用关联部位的项目')

# 检查部位使用统计表是否存在
try:
    stats_df = pd.read_excel('output/完整检查项目清单_20250711_230350.xlsx', sheet_name='部位使用统计')
    print('\n部位使用统计表存在，内容:')
    print(stats_df)
except Exception as e:
    print(f'\n部位使用统计表不存在: {e}')

# 检查Excel文件中的所有sheet
xl_file = pd.ExcelFile('output/完整检查项目清单_20250711_230350.xlsx')
print(f'\nExcel文件包含的所有sheet: {xl_file.sheet_names}')