#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医保项目生成结果验证脚本
验证关联部位编码是否正确应用
"""

import pandas as pd
import os
from datetime import datetime

def find_latest_medical_insurance_file():
    """查找最新的医保检查项目清单文件"""
    output_dir = 'output'
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录 {output_dir} 不存在")
        return None
    
    # 查找所有医保检查项目清单文件
    files = [f for f in os.listdir(output_dir) if f.startswith('医保检查项目清单_') and f.endswith('.xlsx')]
    
    if not files:
        print(f"❌ 在 {output_dir} 目录中未找到医保检查项目清单文件")
        return None
    
    # 按文件名排序，获取最新的文件
    files.sort(reverse=True)
    latest_file = os.path.join(output_dir, files[0])
    print(f"📁 找到最新文件: {latest_file}")
    return latest_file

def verify_medical_insurance_results():
    """验证医保项目生成结果"""
    print("🔍 开始验证医保项目生成结果...\n")
    
    # 查找最新文件
    latest_file = find_latest_medical_insurance_file()
    if not latest_file:
        return
    
    try:
        # 读取Excel文件的所有sheet
        excel_file = pd.ExcelFile(latest_file)
        print(f"📊 Excel文件包含的sheet: {excel_file.sheet_names}\n")
        
        # 读取医保检查项目清单
        df_projects = pd.read_excel(latest_file, sheet_name='医保检查项目清单')
        print(f"📋 医保检查项目清单总数: {len(df_projects)} 个项目\n")
        
        # 分析使用关联部位的项目
        related_projects = df_projects[df_projects['使用关联部位'] == '是']
        default_projects = df_projects[df_projects['使用关联部位'] == '否']
        
        print(f"📊 项目分类统计:")
        print(f"   - 使用关联部位: {len(related_projects)} 个")
        print(f"   - 使用默认部位: {len(default_projects)} 个\n")
        
        # 详细分析关联部位项目
        if len(related_projects) > 0:
            print("🔍 使用关联部位的项目详情:")
            for idx, project in related_projects.iterrows():
                print(f"   {idx+1}. {project['检查项目名称']}")
                print(f"      关联部位: {project['关联部位']}")
                print(f"      关联部位编码: {project['关联部位编码']}")
                print(f"      实际部位编码: {project['部位编码']}")
                print(f"      一级部位: {project['一级部位']}")
                print(f"      二级部位: {project['二级部位']}")
                print(f"      三级部位: {project['三级部位']}")
                print(f"      项目编码: {project['检查项目编码']}")
                print()
        
        # 验证部位编码一致性
        print("🔍 验证部位编码一致性:")
        inconsistent_count = 0
        for idx, project in related_projects.iterrows():
            expected_code = str(int(float(project['关联部位编码']))).zfill(5)
            actual_code = str(project['部位编码'])
            if expected_code != actual_code:
                print(f"   ❌ 不一致: {project['检查项目名称']}")
                print(f"      期望部位编码: {expected_code}")
                print(f"      实际部位编码: {actual_code}")
                inconsistent_count += 1
        
        if inconsistent_count == 0:
            print("   ✅ 所有关联部位项目的部位编码都正确")
        else:
            print(f"   ❌ 发现 {inconsistent_count} 个部位编码不一致的项目")
        
        # 验证默认部位项目
        print("\n🔍 验证默认部位项目:")
        wrong_default_count = 0
        for idx, project in default_projects.iterrows():
            # 默认部位编码应该是90000（非指定部位）
            if str(project['部位编码']).strip() != '90000':
                print(f"   ❌ 默认部位编码错误: {project['检查项目名称']} - {project['部位编码']} (应为90000)")
                wrong_default_count += 1
        
        if wrong_default_count == 0:
            print("   ✅ 所有默认部位项目的部位编码都正确 (90000 - 非指定部位)")
        else:
            print(f"   ❌ 发现 {wrong_default_count} 个默认部位编码错误的项目")
        
        # 读取统计信息
        if '统计信息' in excel_file.sheet_names:
            df_stats = pd.read_excel(latest_file, sheet_name='统计信息')
            print("\n📊 统计信息:")
            for idx, row in df_stats.iterrows():
                print(f"   {row['统计项']}: {row['数值']}")
        
        # 读取部位使用统计
        if '部位使用统计' in excel_file.sheet_names:
            df_part_usage = pd.read_excel(latest_file, sheet_name='部位使用统计')
            print("\n📊 部位使用统计:")
            for idx, row in df_part_usage.iterrows():
                print(f"   {row['部位类型']}: {row['项目数量']} 个")
        
        print("\n✅ 验证完成！")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {str(e)}")

if __name__ == "__main__":
    verify_medical_insurance_results()