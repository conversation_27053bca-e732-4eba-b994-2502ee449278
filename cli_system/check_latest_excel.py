#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查最新生成的Excel文件中的MRLH项目
"""

import pandas as pd
import os

def check_latest_excel():
    """检查最新Excel文件中的MRLH项目"""
    print("🔍 检查最新生成的Excel文件中的MRLH项目")
    print("=" * 50)
    
    # 查找最新的Excel文件
    output_dir = "output"
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return
    
    files = [f for f in os.listdir(output_dir) if f.startswith('完整检查项目清单_') and f.endswith('.xlsx')]
    if not files:
        print("❌ 未找到输出文件")
        return
    
    latest_file = max(files, key=lambda x: os.path.getctime(os.path.join(output_dir, x)))
    latest_path = os.path.join(output_dir, latest_file)
    
    print(f"📁 最新文件: {latest_file}")
    
    try:
        # 读取MR项目sheet
        df_mr = pd.read_excel(latest_path, sheet_name='MR项目')
        print(f"📊 MR项目总数: {len(df_mr)}")
        
        # 查找MRLH项目
        mrlh_mask = df_mr['扫描方式'].str.contains('MRLH', na=False)
        mrlh_projects = df_mr[mrlh_mask]
        
        print(f"🔍 MRLH项目数: {len(mrlh_projects)}")
        
        if len(mrlh_projects) > 0:
            print("\n📋 MRLH项目详情:")
            for idx, row in mrlh_projects.iterrows():
                print(f"  {idx+1}. {row['检查项目名称']}")
                print(f"      编码: {row['检查项目编码']}")
                print(f"      部位: {row['三级部位']}")
                print(f"      扫描方式: {row['扫描方式']}")
                print()
        else:
            print("❌ 未找到MRLH项目")
        
        # 统计所有MR扫描方式
        print("\n📊 所有MR扫描方式统计:")
        scan_counts = df_mr['扫描方式'].value_counts()
        for scan_type, count in scan_counts.items():
            print(f"  {scan_type}: {count}个")
        
        # 检查MR-2 sheet
        print("\n🔍 检查MR-2 sheet:")
        try:
            df_mr2 = pd.read_excel(latest_path, sheet_name='MR-2')
            mrlh_mask_v2 = df_mr2['扫描方式'].str.contains('MRLH', na=False)
            mrlh_projects_v2 = df_mr2[mrlh_mask_v2]
            print(f"📊 MR-2项目总数: {len(df_mr2)}")
            print(f"🔍 MR-2中MRLH项目数: {len(mrlh_projects_v2)}")
        except Exception as e:
            print(f"❌ 读取MR-2 sheet失败: {e}")
        
        # 检查所有sheet名称
        print("\n📋 Excel文件中的所有sheet:")
        xl_file = pd.ExcelFile(latest_path)
        for sheet_name in xl_file.sheet_names:
            print(f"  - {sheet_name}")
            
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_latest_excel()