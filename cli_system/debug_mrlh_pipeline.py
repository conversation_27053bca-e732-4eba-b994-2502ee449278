#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试MR水成像MRLH项目在unified_pipeline中的处理流程
检查项目在各个阶段是否被正确保留
"""

import pandas as pd
import os
from datetime import datetime
from medical_project_generator import MedicalProjectGenerator
from unified_pipeline import UnifiedMedicalPipeline

def debug_mrlh_in_pipeline():
    """调试MRLH项目在pipeline中的处理过程"""
    print("🔍 调试MR水成像MRLH项目在unified_pipeline中的处理流程")
    print("=" * 70)
    
    excel_file = "data/NEW_检查项目名称结构表 (11).xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 数据文件不存在: {excel_file}")
        return
    
    # 1. 直接使用MedicalProjectGenerator检查MRLH项目生成
    print("\n📋 步骤1: 检查MedicalProjectGenerator中的MRLH项目生成")
    print("-" * 50)
    
    generator = MedicalProjectGenerator(excel_file)
    projects = generator.generate_projects()
    
    # 查找MRLH项目
    mrlh_projects = [p for p in projects if 'MRLH' in p.get('扫描方式', '') or 'MR水成像MRLH' in p.get('扫描方式', '')]
    print(f"✅ MedicalProjectGenerator生成的MRLH项目数: {len(mrlh_projects)}")
    
    if mrlh_projects:
        print("\n🔍 MRLH项目详情:")
        for i, project in enumerate(mrlh_projects, 1):
            print(f"  {i}. {project['检查项目名称']} - {project['三级部位']} - {project['扫描方式']}")
    
    # 2. 检查UnifiedMedicalPipeline的process_ctmr_projects方法
    print("\n📋 步骤2: 检查UnifiedMedicalPipeline的CT/MR处理")
    print("-" * 50)
    
    pipeline = UnifiedMedicalPipeline(excel_file)
    pipeline.process_ctmr_projects()
    
    if 'ctmr' in pipeline.results and 'error' not in pipeline.results['ctmr']:
        ctmr_data = pipeline.results['ctmr']
        all_projects = ctmr_data['all_projects']
        mr_projects = ctmr_data['mr_projects']
        
        # 在所有项目中查找MRLH
        mrlh_in_all = [p for p in all_projects if 'MRLH' in p.get('扫描方式', '') or 'MR水成像MRLH' in p.get('扫描方式', '')]
        print(f"✅ Pipeline all_projects中的MRLH项目数: {len(mrlh_in_all)}")
        
        # 在MR项目中查找MRLH
        mrlh_in_mr = [p for p in mr_projects if 'MRLH' in p.get('扫描方式', '') or 'MR水成像MRLH' in p.get('扫描方式', '')]
        print(f"✅ Pipeline mr_projects中的MRLH项目数: {len(mrlh_in_mr)}")
        
        if mrlh_in_mr:
            print("\n🔍 Pipeline中的MRLH项目详情:")
            for i, project in enumerate(mrlh_in_mr, 1):
                print(f"  {i}. {project['检查项目名称']} - {project['三级部位']} - {project['扫描方式']}")
        
        # 3. 检查V2格式处理
        print("\n📋 步骤3: 检查V2格式处理")
        print("-" * 50)
        
        mr_projects_v2 = ctmr_data.get('mr_projects_v2', [])
        mrlh_in_v2 = [p for p in mr_projects_v2 if 'MRLH' in p.get('扫描方式', '') or 'MR水成像MRLH' in p.get('扫描方式', '')]
        print(f"✅ Pipeline mr_projects_v2中的MRLH项目数: {len(mrlh_in_v2)}")
        
        if mrlh_in_v2:
            print("\n🔍 V2格式中的MRLH项目详情:")
            for i, project in enumerate(mrlh_in_v2, 1):
                print(f"  {i}. {project['检查项目名称']} - {project['三级部位']} - {project['扫描方式']}")
    
    else:
        print("❌ CT/MR处理失败或出现错误")
        if 'ctmr' in pipeline.results:
            print(f"   错误信息: {pipeline.results['ctmr'].get('error', '未知错误')}")
    
    # 4. 检查完整pipeline运行
    print("\n📋 步骤4: 运行完整Pipeline并检查输出文件")
    print("-" * 50)
    
    try:
        output_file = pipeline.run_full_pipeline()
        print(f"✅ Pipeline运行完成，输出文件: {output_file}")
        
        # 检查输出文件中的MRLH项目
        if os.path.exists(output_file):
            print("\n🔍 检查输出Excel文件中的MRLH项目:")
            
            # 检查MR项目sheet
            try:
                df_mr = pd.read_excel(output_file, sheet_name='MR项目')
                mrlh_rows = df_mr[df_mr['扫描方式'].str.contains('MRLH', na=False)]
                print(f"  📊 MR项目sheet中的MRLH项目数: {len(mrlh_rows)}")
                
                if len(mrlh_rows) > 0:
                    print("    MRLH项目详情:")
                    for idx, row in mrlh_rows.iterrows():
                        print(f"      - {row['检查项目名称']} ({row['三级部位']})")
                else:
                    print("    ❌ MR项目sheet中未找到MRLH项目")
                    
            except Exception as e:
                print(f"  ❌ 读取MR项目sheet失败: {e}")
            
            # 检查MR-2项目sheet
            try:
                df_mr2 = pd.read_excel(output_file, sheet_name='MR-2')
                mrlh_rows_v2 = df_mr2[df_mr2['扫描方式'].str.contains('MRLH', na=False)]
                print(f"  📊 MR-2项目sheet中的MRLH项目数: {len(mrlh_rows_v2)}")
                
                if len(mrlh_rows_v2) > 0:
                    print("    MRLH项目详情:")
                    for idx, row in mrlh_rows_v2.iterrows():
                        print(f"      - {row['检查项目名称']} ({row['三级部位']})")
                else:
                    print("    ❌ MR-2项目sheet中未找到MRLH项目")
                    
            except Exception as e:
                print(f"  ❌ 读取MR-2项目sheet失败: {e}")
        
    except Exception as e:
        print(f"❌ 运行完整Pipeline失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. 总结分析
    print("\n📋 步骤5: 问题分析总结")
    print("-" * 50)
    
    print("\n🔍 可能的问题原因:")
    print("1. 数据源问题: 检查扫描方式医保映射编码表中MRLH的配置")
    print("2. 过滤逻辑问题: 检查get_applicable_scans_for_part方法的过滤条件")
    print("3. 数据处理问题: 检查unified_pipeline中的数据转换和格式化")
    print("4. 导出问题: 检查Excel导出过程中的数据丢失")
    print("5. 命名问题: 检查项目名称生成逻辑")
    
    print("\n✅ 调试完成")

if __name__ == "__main__":
    debug_mrlh_in_pipeline()