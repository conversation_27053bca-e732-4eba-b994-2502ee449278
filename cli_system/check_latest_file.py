#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
import glob
from datetime import datetime

def check_latest_excel_file():
    """检查最新的Excel文件是否包含部位使用统计表"""
    try:
        # 查找最新的完整检查项目清单文件
        pattern = 'cli_system/output/完整检查项目清单_*.xlsx'
        files = glob.glob(pattern)
        
        if not files:
            print("❌ 未找到完整检查项目清单文件")
            return
        
        # 获取最新文件
        latest_file = max(files, key=os.path.getctime)
        print(f"📁 检查文件: {latest_file}")
        
        # 读取Excel文件
        xl = pd.ExcelFile(latest_file)
        sheets = xl.sheet_names
        
        print(f"\n📋 所有sheet名称 (共{len(sheets)}个):")
        for i, sheet in enumerate(sheets, 1):
            print(f"  {i:2d}. {sheet}")
        
        # 检查医保相关的统计表
        insurance_sheets = [s for s in sheets if '医保' in s or '部位' in s]
        print(f"\n📊 医保相关统计表:")
        for sheet in insurance_sheets:
            print(f"  ✅ {sheet}")
        
        # 检查部位使用统计表
        if '部位使用统计' in sheets:
            print(f"\n🎉 找到部位使用统计表!")
            df_stats = pd.read_excel(latest_file, sheet_name='部位使用统计')
            print(f"\n📈 部位使用统计表内容:")
            print(df_stats.to_string(index=False))
        else:
            print(f"\n❌ 未找到部位使用统计表")
        
        # 检查医保项目表中的使用关联部位字段
        if '医保项目' in sheets:
            print(f"\n🔍 检查医保项目表中的使用关联部位字段:")
            df_insurance = pd.read_excel(latest_file, sheet_name='医保项目')
            if '使用关联部位' in df_insurance.columns:
                usage_stats = df_insurance['使用关联部位'].value_counts()
                print(f"使用关联部位统计:")
                print(usage_stats.to_string())
            else:
                print(f"❌ 医保项目表中未找到'使用关联部位'字段")
                print(f"可用字段: {list(df_insurance.columns)}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_latest_excel_file()