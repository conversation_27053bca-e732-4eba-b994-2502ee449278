#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目处理系统 - 增强版启动脚本
包含详细项目说明和移除CT&MR合并表格
"""

import os
import sys
from datetime import datetime

# 添加src目录到Python路径
src_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, src_dir)

from unified_pipeline import UnifiedMedicalPipeline

def main():
    """主函数 - 启动增强版医疗检查项目处理系统"""
    print("🏥 医疗检查项目处理系统 - 增强版")
    print("=" * 70)
    print("📋 系统功能:")
    print("   ✅ 生成CT、MR、DR、MG、医保五大类检查项目")
    print("   ✅ 完整的字段说明和技术规范文档")
    print("   ✅ 支持体位、方向编码的DR项目增强")
    print("   ✅ 医保映射码优化排序")
    print("   ✅ 数据质量验证和重复检测")
    print("   ✅ 参考数据表集成")
    print("=" * 70)
    
    try:
        # 数据文件路径（相对路径）
        excel_file = "data/NEW_检查项目名称结构表 (11).xlsx"
        
        # 检查文件是否存在
        if not os.path.exists(excel_file):
            print(f"❌ 数据文件不存在: {excel_file}")
            print("   请确认数据文件位置正确")
            return False
        
        print(f"📂 数据源文件: {excel_file}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 创建并运行Pipeline
        pipeline = UnifiedMedicalPipeline(excel_file)
        output_file = pipeline.run_full_pipeline()
        
        print("\n" + "=" * 70)
        print("🎉 处理完成!")
        print(f"📁 输出文件: {output_file}")
        print("\n📋 输出内容包含:")
        print("   📊 统计信息 - 整体数据统计")
        print("   📖 项目说明 - 详细的字段含义和规范")
        print("   🔬 CT项目 - CT检查项目清单")
        print("   🧠 MR项目 - MR检查项目清单") 
        print("   📷 DR项目 - DR检查项目清单（增强体位信息）")
        print("   🩻 MG项目 - 乳腺钼靶检查项目")
        print("   💰 医保项目 - 医保对接项目清单")
        print("   📋 CT-2/MR-2/DR-2/MG-2/医保-2 - 医保映射码排序版本")
        print("   📂 6个参考数据表 - 基础数据支撑")
        print("\n✨ 所有项目已按最新规范生成，包含完整的技术文档说明!")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎯 提示: 如需重新运行，请执行: python run_enhanced_pipeline.py")
    else:
        print(f"\n❌ 执行失败，请检查错误信息并重试")
        sys.exit(1)