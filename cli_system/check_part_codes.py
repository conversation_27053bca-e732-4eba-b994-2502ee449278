#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查部位编码数据
"""

import pandas as pd

def check_part_codes():
    """检查部位编码数据"""
    try:
        df = pd.read_excel('data/NEW_检查项目名称结构表 (11).xlsx', sheet_name='三级部位结构')
        
        print("📊 部位编码数据分析:")
        print(f"总记录数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        print("\n📋 前10条记录:")
        print(df[['一级部位', '二级部位', '三级部位', '部位编码']].head(10).to_string())
        
        print("\n🔍 部位编码数据类型分析:")
        print(f"部位编码列数据类型: {df['部位编码'].dtype}")
        print(f"部位编码唯一值数量: {df['部位编码'].nunique()}")
        
        # 检查部位编码的值范围
        numeric_codes = pd.to_numeric(df['部位编码'], errors='coerce')
        valid_codes = numeric_codes.dropna()
        
        if len(valid_codes) > 0:
            print(f"数值型部位编码范围: {valid_codes.min()} - {valid_codes.max()}")
        
        # 查找包含"非指定"的部位
        print("\n🔍 查找包含'非指定'的部位:")
        non_specified = df[df['一级部位'].str.contains('非指定', na=False)]
        if len(non_specified) > 0:
            print(non_specified[['一级部位', '二级部位', '三级部位', '部位编码']].to_string())
        else:
            print("未找到包含'非指定'的部位")
        
        # 查找包含"其他"的部位
        print("\n🔍 查找包含'其他'的部位:")
        other_parts = df[df['三级部位'].str.contains('其他', na=False)]
        if len(other_parts) > 0:
            print(other_parts[['一级部位', '二级部位', '三级部位', '部位编码']].to_string())
        else:
            print("未找到包含'其他'的部位")
        
        # 查找部位编码为90000或90200的记录
        print("\n🔍 查找部位编码为90000或90200的记录:")
        code_90000 = df[df['部位编码'].astype(str).str.strip() == '90000']
        code_90200 = df[df['部位编码'].astype(str).str.strip() == '90200']
        
        if len(code_90000) > 0:
            print("部位编码90000:")
            print(code_90000[['一级部位', '二级部位', '三级部位', '部位编码']].to_string())
        
        if len(code_90200) > 0:
            print("部位编码90200:")
            print(code_90200[['一级部位', '二级部位', '三级部位', '部位编码']].to_string())
        
        if len(code_90000) == 0 and len(code_90200) == 0:
            print("未找到部位编码为90000或90200的记录")
        
        # 查看最大的部位编码
        print("\n🔍 最大的几个部位编码:")
        numeric_codes_sorted = valid_codes.sort_values(ascending=False)
        max_codes = numeric_codes_sorted.head(10)
        for code in max_codes:
            matching_rows = df[pd.to_numeric(df['部位编码'], errors='coerce') == code]
            if len(matching_rows) > 0:
                row = matching_rows.iloc[0]
                print(f"  {int(code)}: {row['一级部位']} - {row['二级部位']} - {row['三级部位']}")
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

if __name__ == "__main__":
    check_part_codes()