#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试医保项目编码格式修复
验证关联部位编码是否正确处理为字符串格式
"""

import pandas as pd
from medical_insurance_generator import MedicalInsuranceGenerator
import os
from datetime import datetime

def test_encoding_format():
    """测试编码格式处理"""
    print("🧪 测试医保项目编码格式修复")
    print("=" * 50)
    
    try:
        # 初始化生成器
        excel_file = "data/NEW_检查项目名称结构表 (11).xlsx"
        generator = MedicalInsuranceGenerator(excel_file)
        
        # 生成项目
        projects = generator.generate_projects()
        
        print(f"\n📊 编码格式验证:")
        
        # 检查关联部位编码格式
        related_projects = [p for p in projects if p['使用关联部位'] == '是']
        print(f"\n🔍 关联部位项目编码格式检查 ({len(related_projects)} 个项目):")
        
        for project in related_projects:
            code = project['关联部位编码']
            print(f"   - {project['检查项目名称'][:30]}... 编码: {code} (类型: {type(code).__name__})")
            
            # 验证编码是否为纯数字字符串
            if code and code.isdigit():
                print(f"     ✅ 编码格式正确: {code}")
            elif code:
                print(f"     ⚠️  编码格式异常: {code}")
            else:
                print(f"     ❌ 编码为空")
        
        # 检查所有编码字段的数据类型
        print(f"\n🔍 所有编码字段数据类型检查:")
        encoding_fields = ['一级编码', '二级编码', '三级编码', '部位编码', 
                          '检查项目编码', '医保映射码', '医保扩展码', '关联部位编码']
        
        for field in encoding_fields:
            if projects:
                sample_value = projects[0][field]
                print(f"   - {field}: {sample_value} (类型: {type(sample_value).__name__})")
        
        # 导出测试结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"output/编码格式测试_{timestamp}.xlsx"
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 创建DataFrame并导出
        df_projects = pd.DataFrame(projects)
        
        # 创建编码格式分析表
        encoding_analysis = []
        for project in projects:
            if project['使用关联部位'] == '是':
                encoding_analysis.append({
                    '项目名称': project['检查项目名称'],
                    '关联部位': project['关联部位'],
                    '关联部位编码': project['关联部位编码'],
                    '编码类型': type(project['关联部位编码']).__name__,
                    '是否纯数字': str(project['关联部位编码']).isdigit() if project['关联部位编码'] else False,
                    '编码长度': len(str(project['关联部位编码'])) if project['关联部位编码'] else 0
                })
        
        df_analysis = pd.DataFrame(encoding_analysis)
        
        # 写入Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df_projects.to_excel(writer, sheet_name='医保项目', index=False)
            df_analysis.to_excel(writer, sheet_name='编码格式分析', index=False)
        
        print(f"\n✅ 测试完成！结果已导出到: {output_file}")
        print(f"📄 文件包含:")
        print(f"   - 医保项目: {len(projects)} 个项目")
        print(f"   - 编码格式分析: {len(encoding_analysis)} 个关联部位项目")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_encoding_format()