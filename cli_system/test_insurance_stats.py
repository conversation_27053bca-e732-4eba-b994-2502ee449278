#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
from datetime import datetime

def test_insurance_statistics():
    """测试医保项目统计表生成"""
    try:
        # 读取现有的医保项目数据
        file_path = 'cli_system/output/完整检查项目清单_20250711_230350.xlsx'
        df_insurance = pd.read_excel(file_path, sheet_name='医保项目')
        
        print(f"📋 医保项目数据加载成功: {len(df_insurance)}行")
        print(f"📋 字段列表: {list(df_insurance.columns)}")
        
        # 检查关键字段
        if '使用关联部位' in df_insurance.columns:
            print(f"\n✅ 找到'使用关联部位'字段")
            usage_stats = df_insurance['使用关联部位'].value_counts()
            print(f"使用关联部位统计:")
            print(usage_stats.to_string())
        else:
            print(f"❌ 未找到'使用关联部位'字段")
            return
        
        # 创建测试输出文件
        output_file = f'cli_system/output/测试医保统计表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
        # 使用ExcelWriter生成统计表
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 原始医保项目数据
            df_insurance.to_excel(writer, sheet_name='医保项目', index=False)
            
            # 模态统计
            modality_counts = df_insurance['模态'].value_counts().reset_index()
            modality_counts.columns = ['模态', '项目数量']
            modality_counts.to_excel(writer, sheet_name='医保模态统计', index=False)
            print(f"✅ 已添加医保模态统计表: {len(modality_counts)}种模态")
            
            # 归集口径统计
            if '归集口径' in df_insurance.columns:
                category_counts = df_insurance['归集口径'].value_counts().reset_index()
                category_counts.columns = ['归集口径', '项目数量']
                category_counts.to_excel(writer, sheet_name='医保归集口径统计', index=False)
                print(f"✅ 已添加医保归集口径统计表: {len(category_counts)}种归集口径")
            
            # 部位使用统计
            if '使用关联部位' in df_insurance.columns:
                part_usage_counts = df_insurance['使用关联部位'].value_counts().reset_index()
                part_usage_counts.columns = ['部位类型', '项目数量']
                
                # 映射值
                part_usage_counts['部位类型'] = part_usage_counts['部位类型'].map({
                    '是': '使用关联部位', 
                    '否': '使用默认部位'
                })
                
                part_usage_counts.to_excel(writer, sheet_name='部位使用统计', index=False)
                print(f"✅ 已添加部位使用统计表: {len(part_usage_counts)}种部位类型")
                
                # 显示统计表内容
                print(f"\n📈 部位使用统计表内容:")
                print(part_usage_counts.to_string(index=False))
            
        print(f"\n🎉 测试完成，输出文件: {output_file}")
        
        # 验证生成的文件
        xl = pd.ExcelFile(output_file)
        sheets = xl.sheet_names
        print(f"\n📋 生成的sheet列表:")
        for i, sheet in enumerate(sheets, 1):
            print(f"  {i}. {sheet}")
        
        if '部位使用统计' in sheets:
            print(f"\n✅ 部位使用统计表成功生成!")
        else:
            print(f"\n❌ 部位使用统计表生成失败")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_insurance_statistics()