#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医保项目检查清单生成器
基于 NEW_检查项目名称结构表 (11).xlsx 中的 医保编码 和 模态表 数据源

根据用户要求生成医保检查项目：
- 名称：使用医保项目名称
- 编码格式：16位 = [医保映射码6位][部位编码6位][医保扩展码2位][人群编码1位][疾病编码1位][平急诊编码1位]
- 部位编码：90200（其他）
- 扩展码：00
- 人群编码、疾病编码、平急诊编码：都为0
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class MedicalInsuranceGenerator:
    """医保项目生成器"""
    
    def __init__(self, excel_file_path):
        """初始化生成器"""
        self.excel_file_path = excel_file_path
        self.df_medical = None
        self.df_modality = None
        self.df_parts = None
        self.load_data()
        
    def load_data(self):
        """加载数据源"""
        print("🔄 正在加载数据源...")
        try:
            # 加载医保编码sheet
            self.df_medical = pd.read_excel(self.excel_file_path, sheet_name='医保编码')
            print(f"✅ 医保编码: {self.df_medical.shape[0]} 行 x {self.df_medical.shape[1]} 列")
            
            # 加载模态表
            self.df_modality = pd.read_excel(self.excel_file_path, sheet_name='模态表')
            print(f"✅ 模态表: {self.df_modality.shape[0]} 行 x {self.df_modality.shape[1]} 列")
            
            # 加载三级部位结构（获取"其他"部位信息）
            self.df_parts = pd.read_excel(self.excel_file_path, sheet_name='三级部位结构')
            print(f"✅ 三级部位结构: {self.df_parts.shape[0]} 行 x {self.df_parts.shape[1]} 列")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise
    
    def create_modality_mapping(self):
        """创建模态映射字典"""
        print("\n📚 正在创建模态映射字典...")
        
        modality_dict = {}
        # 基于第一位数字的默认映射（处理包含字母的情况）
        first_digit_mapping = {
            '1': 'DR',  # 1x -> DR
            '2': 'CT',  # 2x -> CT  
            '3': 'MR',  # 3x -> MR
            '4': 'OT'   # 4x -> OT
        }
        
        # 从模态表构建精确映射
        for _, row in self.df_modality.iterrows():
            first_digit = str(row['医保第一位'])
            second_digit = str(int(row['第二位'])) if not pd.isna(row['第二位']) else '0'
            
            # 构建前两位的映射
            key = f"{first_digit}{second_digit}"
            modality_dict[key] = row['模态']
        
        print(f"✅ 精确模态映射: {modality_dict}")
        print(f"✅ 第一位默认映射: {first_digit_mapping}")
        
        # 返回两个映射
        return modality_dict, first_digit_mapping
    
    def get_other_part_info(self):
        """获取"其他"部位的信息"""
        print("\n🔍 正在查找\"其他\"部位信息...")
        
        other_parts = self.df_parts[self.df_parts['三级部位'].str.contains('其他', na=False)]
        if len(other_parts) > 0:
            other_part = other_parts.iloc[0]
            part_info = {
                '一级编码': str(other_part['一级编码']).split('.')[0],
                '一级部位': str(other_part['一级部位']),
                '二级编码': str(other_part['二级编码']).split('.')[0].zfill(2),
                '二级部位': str(other_part['二级部位']),
                '三级编码': str(other_part['三级编码']).split('.')[0].zfill(2),
                '三级部位': str(other_part['三级部位']),
                '部位编码': str(other_part['部位编码']).split('.')[0].zfill(5)
            }
            print(f"✅ 找到\"其他\"部位: {part_info}")
            return part_info
        else:
            # 如果没找到，使用默认值 - 按照 9	非指定部位	00	-	00	-	90000
            part_info = {
                '一级编码': '9',
                '一级部位': '非指定部位',
                '二级编码': '00',
                '二级部位': '-',
                '三级编码': '00',
                '三级部位': '-',
                '部位编码': '90000'
            }
            print(f"⚠️  未找到\"其他\"部位，使用默认值: {part_info}")
            return part_info
    
    def get_part_info_by_code(self, part_code):
        """根据部位编码获取部位信息"""
        if pd.isna(part_code):
            return None
            
        # 将部位编码转换为字符串并补齐5位，处理浮点格式
        part_code_str = str(part_code).split('.')[0].zfill(5)
        
        # 在三级部位结构中查找匹配的部位编码
        matching_parts = self.df_parts[self.df_parts['部位编码'].astype(str).str.zfill(5) == part_code_str]
        
        if len(matching_parts) > 0:
            part = matching_parts.iloc[0]
            part_info = {
                '一级编码': str(part['一级编码']).split('.')[0],
                '一级部位': str(part['一级部位']),
                '二级编码': str(part['二级编码']).split('.')[0].zfill(2),
                '二级部位': str(part['二级部位']),
                '三级编码': str(part['三级编码']).split('.')[0].zfill(2),
                '三级部位': str(part['三级部位']),
                '部位编码': part_code_str
            }
            return part_info
        else:
            print(f"⚠️  未找到部位编码 {part_code_str} 对应的部位信息")
            return None
    
    def determine_modality(self, medical_code, modality_dict, first_digit_mapping):
        """根据医保映射码确定模态"""
        medical_code = str(medical_code)
        if len(medical_code) >= 2:
            first_two = medical_code[:2]
            # 优先使用精确映射
            if first_two in modality_dict:
                return modality_dict[first_two]
            # 如果精确映射没有找到，使用第一位数字的默认映射
            first_digit = medical_code[0]
            if first_digit in first_digit_mapping:
                return first_digit_mapping[first_digit]
        return 'Unknown'
    
    def generate_project_code(self, medical_code, part_code, extension_code):
        """生成16位项目编码"""
        # 医保映射码（6位）
        medical_code = str(medical_code).ljust(6, '0')[:6]
        
        # 部位编码（5位）
        part_code = str(part_code).zfill(5)[:5]
        
        # 医保扩展码（2位）- 使用实际的扩展码
        extension_code = self.format_extension_code(extension_code)
        
        # 人群编码（1位）
        population_code = "0"
        
        # 疾病编码（1位）  
        disease_code = "0"
        
        # 平急诊编码（1位）
        emergency_code = "0"
        
        # 拼接16位编码
        full_code = medical_code + part_code + extension_code + population_code + disease_code + emergency_code
        
        return full_code
    
    def format_extension_code(self, extension_code):
        """格式化医保扩展码为2位"""
        if pd.isna(extension_code):
            return "00"
        
        code_str = str(extension_code).strip().upper()
        
        # 处理特殊值 "xx" -> "99"
        if code_str == "XX":
            return "99"
        
        # 如果是数字，确保为2位
        if code_str.isdigit():
            return code_str.zfill(2)[:2]
        
        # 如果是其他格式，尝试提取数字
        import re
        numbers = re.findall(r'\d+', code_str)
        if numbers:
            return numbers[0].zfill(2)[:2]
        
        # 默认返回00
        return "00"
    
    def generate_projects(self):
        """生成医保检查项目"""
        print(f"\n🏭 正在生成医保检查项目清单...")
        
        modality_dict, first_digit_mapping = self.create_modality_mapping()
        other_part_info = self.get_other_part_info()
        
        projects = []
        related_part_count = 0  # 统计使用关联部位的项目数量
        
        for idx, row in self.df_medical.iterrows():
            # 确定模态
            modality = self.determine_modality(row['医保映射码'], modality_dict, first_digit_mapping)
            
            # 获取并格式化医保扩展码
            formatted_extension_code = self.format_extension_code(row['医保扩展码'])
            
            # 确定使用的部位信息
            part_info = other_part_info  # 默认使用"其他"部位
            use_related_part = False
            
            # 检查是否有关联部位和关联部位编码
            if ('关联部位' in row and not pd.isna(row['关联部位']) and 
                '关联部位编码' in row and not pd.isna(row['关联部位编码'])):
                
                # 根据关联部位编码获取部位信息
                related_part_info = self.get_part_info_by_code(row['关联部位编码'])
                if related_part_info:
                    part_info = related_part_info
                    use_related_part = True
                    related_part_count += 1
                    print(f"✅ 项目 '{row['医保项目名称']}' 使用关联部位: {row['关联部位']} (编码: {row['关联部位编码']})")
            
            # 生成项目编码
            project_code = self.generate_project_code(row['医保映射码'], part_info['部位编码'], row['医保扩展码'])
            
            # 创建项目记录
            project = {
                '模态': modality,
                '一级编码': part_info['一级编码'],
                '一级部位': part_info['一级部位'], 
                '二级编码': part_info['二级编码'],
                '二级部位': part_info['二级部位'],
                '三级编码': part_info['三级编码'],
                '部位编码': part_info['部位编码'],
                '三级部位': part_info['三级部位'],
                '医保映射码': str(row['医保映射码']).ljust(6, '0')[:6],
                '医保扩展码': formatted_extension_code,
                '检查项目名称': str(row['医保项目名称']) if not pd.isna(row['医保项目名称']) else "",
                '检查项目编码': project_code,
                '人群编码': '0',
                '疾病编码': '0', 
                '平急诊编码': '0',
                # 保留原始字段
                '序号': str(row['序号']) if not pd.isna(row['序号']) else "",
                '归集口径': str(row['归集口径']) if not pd.isna(row['归集口径']) else "",
                '医保项目码': str(row['医保项目码']) if not pd.isna(row['医保项目码']) else "",
                '互认项目名称': str(row['互认项目名称']) if not pd.isna(row['互认项目名称']) else "",
                '备注': str(row['备注']) if not pd.isna(row['备注']) else "",
                # 新增字段：标记是否使用关联部位
                '关联部位': str(row['关联部位']) if '关联部位' in row and not pd.isna(row['关联部位']) else "",
                '关联部位编码': str(row['关联部位编码']).split('.')[0] if '关联部位编码' in row and not pd.isna(row['关联部位编码']) else "",
                '使用关联部位': '是' if use_related_part else '否'
            }
            
            projects.append(project)
        
        print(f"✅ 项目生成完成，共生成 {len(projects)} 个医保检查项目")
        print(f"📊 其中 {related_part_count} 个项目使用了关联部位，{len(projects) - related_part_count} 个项目使用默认部位")
        return projects
    
    def validate_projects(self, projects):
        """验证项目数据质量"""
        print(f"\n🔍 正在验证项目数据质量...")
        
        issues = []
        duplicate_projects = []
        
        # 检查项目编码长度
        for project in projects:
            code_length = len(project['检查项目编码'])
            if code_length != 16:
                issues.append(f"项目编码长度错误: {project['检查项目名称']} - {project['检查项目编码']} (长度:{code_length})")
        
        # 检查项目编码唯一性并收集重复项目
        codes = [p['检查项目编码'] for p in projects]
        duplicate_codes = [code for code in set(codes) if codes.count(code) > 1]
        
        if duplicate_codes:
            issues.append(f"发现重复的项目编码: {len(duplicate_codes)} 个重复编码")
            # 收集所有重复编码的项目
            for project in projects:
                if project['检查项目编码'] in duplicate_codes:
                    duplicate_projects.append(project)
        
        # 检查部位编码格式（更新验证逻辑，允许关联部位编码）
        default_part_count = 0
        related_part_count = 0
        for project in projects:
            if project['使用关联部位'] == '是':
                related_part_count += 1
            else:
                default_part_count += 1
                if project['部位编码'] != '90000':
                    issues.append(f"默认部位编码错误: {project['检查项目名称']} - {project['部位编码']} (应为90000)")
        
        print(f"📊 部位编码统计: 默认部位 {default_part_count} 个，关联部位 {related_part_count} 个")
        
        # 输出验证结果
        if issues:
            print("⚠️  发现以下问题:")
            for issue in issues[:10]:  # 最多显示10个问题
                print(f"   - {issue}")
            if duplicate_projects:
                print(f"   - 重复项目详情将导出到单独sheet中，共 {len(duplicate_projects)} 个项目")
        else:
            print("✅ 数据质量验证通过")
        
        return len(issues) == 0, duplicate_projects
    
    def export_results(self, projects, duplicate_projects=None, output_file=None):
        """导出结果到Excel文件"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"output/医保检查项目清单_{timestamp}.xlsx"
        
        print(f"\n📁 正在导出结果到: {output_file}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 创建DataFrame
        df_projects = pd.DataFrame(projects)
        
        # 根据编码格式修正说明，为避免Excel自动转换为数字，在编码字段前加单引号
        def format_encoding_fields(df):
            """格式化编码字段，防止Excel自动转换"""
            encoding_columns = ['一级编码', '二级编码', '三级编码', '部位编码', 
                              '人群编码', '疾病编码', '平急诊编码', '检查项目编码', '医保映射码', '医保扩展码', '关联部位编码']
            
            for col in encoding_columns:
                if col in df.columns:
                    # 添加单引号前缀防止Excel自动转换为数字
                    df[col] = "'" + df[col].astype(str)
            
            return df
        
        # 格式化编码字段
        df_projects = format_encoding_fields(df_projects)
        
        # 创建统计信息
        related_part_count = sum(1 for p in projects if p['使用关联部位'] == '是')
        default_part_count = len(projects) - related_part_count
        
        stats_data = {
            '统计项': ['总项目数', '使用关联部位项目数', '使用默认部位项目数', '项目编码长度验证', '生成时间'],
            '数值': [
                len(projects),
                related_part_count,
                default_part_count,
                '通过' if all(len(p['检查项目编码']) == 16 for p in projects) else '失败',
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ]
        }
        df_stats = pd.DataFrame(stats_data)
        
        # 模态统计
        modality_counts = df_projects['模态'].value_counts().reset_index()
        modality_counts.columns = ['模态', '项目数量']
        
        # 归集口径统计
        category_counts = df_projects['归集口径'].value_counts().reset_index()
        category_counts.columns = ['归集口径', '项目数量']
        
        # 部位使用统计
        part_usage_counts = df_projects['使用关联部位'].value_counts().reset_index()
        part_usage_counts.columns = ['部位类型', '项目数量']
        part_usage_counts['部位类型'] = part_usage_counts['部位类型'].map({'是': '使用关联部位', '否': '使用默认部位'})
        
        # 处理重复项目数据
        df_duplicates = None
        if duplicate_projects and len(duplicate_projects) > 0:
            df_duplicates = pd.DataFrame(duplicate_projects)
            df_duplicates = format_encoding_fields(df_duplicates)
            # 按编码排序，便于查看重复项目
            df_duplicates = df_duplicates.sort_values('检查项目编码')
        
        # 写入Excel文件，包含多个sheet
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df_projects.to_excel(writer, sheet_name='医保检查项目清单', index=False)
            df_stats.to_excel(writer, sheet_name='统计信息', index=False)
            modality_counts.to_excel(writer, sheet_name='模态统计', index=False)
            category_counts.to_excel(writer, sheet_name='归集口径统计', index=False)
            part_usage_counts.to_excel(writer, sheet_name='部位使用统计', index=False)
            
            # 如果有重复项目，添加重复项目sheet
            if df_duplicates is not None:
                df_duplicates.to_excel(writer, sheet_name='重复编码项目', index=False)
        
        print(f"✅ 导出完成！文件包含以下sheet:")
        print(f"   - 医保检查项目清单: {len(projects)} 个项目")
        print(f"   - 统计信息: 生成概览")
        print(f"   - 模态统计: {len(modality_counts)} 个模态")
        print(f"   - 归集口径统计: {len(category_counts)} 个归集口径")
        print(f"   - 部位使用统计: {len(part_usage_counts)} 种部位类型")
        if df_duplicates is not None:
            print(f"   - 重复编码项目: {len(df_duplicates)} 个重复项目")
        
        return output_file

def main():
    """主函数"""
    print("🏥 医保检查项目清单生成器")
    print("=" * 50)
    
    try:
        # 初始化生成器
        excel_file = "data/NEW_检查项目名称结构表 (11).xlsx"
        generator = MedicalInsuranceGenerator(excel_file)
        
        # 生成项目
        projects = generator.generate_projects()
        
        # 验证数据质量
        is_valid, duplicate_projects = generator.validate_projects(projects)
        
        # 导出结果
        output_file = generator.export_results(projects, duplicate_projects)
        
        print(f"\n🎉 医保检查项目清单生成完成！")
        print(f"📄 输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()