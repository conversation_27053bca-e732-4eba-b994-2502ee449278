#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查医保编码sheet的数据结构
"""

import pandas as pd

def check_medical_insurance_data():
    """检查医保编码数据结构"""
    try:
        # 读取医保编码sheet
        df = pd.read_excel('data/NEW_检查项目名称结构表 (11).xlsx', sheet_name='医保编码')
        
        print("医保编码sheet列名:")
        print(df.columns.tolist())
        
        print("\n数据形状:", df.shape)
        
        print("\n前5行数据:")
        print(df.head())
        
        # 检查是否有关联部位相关列
        if '关联部位' in df.columns and '关联部位编码' in df.columns:
            print("\n关联部位相关列的数据:")
            print(df[['医保项目名称', '关联部位', '关联部位编码']].head(10))
            
            # 统计非空值
            print("\n关联部位非空值统计:")
            print(f"关联部位非空: {df['关联部位'].notna().sum()} / {len(df)}")
            print(f"关联部位编码非空: {df['关联部位编码'].notna().sum()} / {len(df)}")
            
            # 显示有关联部位的项目
            has_related_part = df[df['关联部位'].notna() & df['关联部位编码'].notna()]
            if len(has_related_part) > 0:
                print("\n有关联部位的项目:")
                print(has_related_part[['医保项目名称', '关联部位', '关联部位编码']])
        else:
            print("\n未找到关联部位相关列")
            
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    check_medical_insurance_data()