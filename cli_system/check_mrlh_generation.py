#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from medical_project_generator import MedicalProjectGenerator

def check_mrlh_generation():
    """检查MR水成像MRLH项目生成情况"""
    
    print("=== 检查MR水成像MRLH项目生成情况 ===")
    
    # 创建生成器实例
    generator = MedicalProjectGenerator('data/NEW_检查项目名称结构表 (11).xlsx')
    
    # 生成项目
    projects = generator.generate_projects()
    
    # 查找MR水成像MRLH相关项目
    print("\n=== 查找MR水成像MRLH相关项目 ===")
    mrlh_projects = [p for p in projects if 'MRLH' in p['扫描方式'] or 'MR水成像MRLH' in p['扫描方式']]
    
    if mrlh_projects:
        print(f"找到 {len(mrlh_projects)} 个MR水成像MRLH项目:")
        for project in mrlh_projects:
            print(f"- {project['检查项目名称']} (编码: {project['检查项目编码']})")
            print(f"  部位: {project['三级部位']} (部位编码: {project['部位编码']})")
            print(f"  扫描方式: {project['扫描方式']}")
            print()
    else:
        print("❌ 未找到任何MR水成像MRLH项目")
    
    # 检查扫描方式映射
    print("\n=== 检查扫描方式映射 ===")
    scan_mapping = generator.create_scan_mapping_dict()
    
    mrlh_in_mapping = False
    for scan_name, scan_info in scan_mapping.items():
        if 'MRLH' in scan_name or 'MR水成像MRLH' in scan_name:
            print(f"✅ 扫描方式映射中找到: {scan_name}")
            print(f"   医保映射码: {scan_info['医保映射码']}")
            print(f"   医保扩展码: {scan_info['医保扩展码']}")
            mrlh_in_mapping = True
    
    if not mrlh_in_mapping:
        print("❌ 扫描方式映射中未找到MR水成像MRLH")
    
    # 检查部位配置
    print("\n=== 检查部位配置 ===")
    generator.clean_part_data()
    
    # 查找启用了MR水成像MRLH的部位
    mrlh_enabled_parts = []
    for idx, part_row in generator.df_parts.iterrows():
        applicable_scans = generator.get_applicable_scans_for_part(part_row)
        for scan in applicable_scans:
            if 'MRLH' in scan or 'MR水成像MRLH' in scan:
                mrlh_enabled_parts.append({
                    '部位': part_row['三级部位'],
                    '部位编码': part_row['部位编码'],
                    '扫描方式': scan,
                    'MR适用': part_row.get('MR_适用', False)
                })
    
    if mrlh_enabled_parts:
        print(f"✅ 找到 {len(mrlh_enabled_parts)} 个启用MR水成像MRLH的部位:")
        for part in mrlh_enabled_parts:
            print(f"- {part['部位']} (编码: {part['部位编码']}) - MR适用: {part['MR适用']}")
    else:
        print("❌ 未找到启用MR水成像MRLH的部位")
    
    # 详细检查get_applicable_scans_for_part方法
    print("\n=== 详细检查扫描方式获取逻辑 ===")
    
    # 查找包含MR水成像MRLH列的部位
    mrlh_column = None
    for col in generator.df_parts.columns:
        if 'MRLH' in col or 'MR水成像MRLH' in col:
            mrlh_column = col
            break
    
    if mrlh_column:
        print(f"✅ 找到MR水成像MRLH列: {mrlh_column}")
        
        # 查找该列值为1的部位
        enabled_parts = generator.df_parts[generator.df_parts[mrlh_column] == 1]
        print(f"该列值为1的部位数: {len(enabled_parts)}")
        
        for idx, part in enabled_parts.iterrows():
            print(f"\n检查部位: {part['三级部位']} (编码: {part['部位编码']})")
            print(f"MR适用标记: {part.get('MR', 'N/A')}")
            print(f"MR_适用字段: {part.get('MR_适用', 'N/A')}")
            
            # 手动检查该部位的扫描方式获取
            applicable_scans = generator.get_applicable_scans_for_part(part)
            print(f"获取到的适用扫描方式: {applicable_scans}")
            
            # 检查MR相关列的值
            mr_cols = [col for col in generator.df_parts.columns if col.startswith('MR')]
            mr_values = {}
            for col in mr_cols:
                mr_values[col] = part.get(col, 'N/A')
            print(f"所有MR相关列的值: {mr_values}")
    else:
        print("❌ 未找到MR水成像MRLH列")
    
    # 总结
    print("\n=== 问题总结 ===")
    print(f"1. 扫描方式映射表中是否有MRLH: {'是' if mrlh_in_mapping else '否'}")
    print(f"2. 部位表中是否有MRLH列: {'是' if mrlh_column else '否'}")
    print(f"3. 启用MRLH的部位数: {len(mrlh_enabled_parts)}")
    print(f"4. 生成的MRLH项目数: {len(mrlh_projects)}")

if __name__ == "__main__":
    check_mrlh_generation()