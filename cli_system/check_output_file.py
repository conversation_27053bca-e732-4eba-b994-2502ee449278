#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import glob
import os

def check_output_file():
    """检查输出文件中的MR水成像MRLH项目"""
    
    # 查找最新的输出文件
    files = glob.glob('output/CT&MR检查项目清单_*.xlsx')
    if not files:
        print("未找到输出文件")
        return
    
    latest_file = max(files)
    print(f"检查文件: {latest_file}")
    
    try:
        # 读取MR项目sheet
        df_mr = pd.read_excel(latest_file, sheet_name='MR项目')
        print(f"\nMR项目总数: {len(df_mr)}")
        
        # 查找MR水成像MRLH项目
        mrlh_projects = df_mr[df_mr['扫描方式'].str.contains('MRLH', na=False)]
        print(f"MR水成像MRLH项目数: {len(mrlh_projects)}")
        
        if len(mrlh_projects) > 0:
            print("\nMR水成像MRLH项目详情:")
            for idx, row in mrlh_projects.iterrows():
                print(f"{idx+1}. {row['检查项目名称']} - {row['检查项目编码']}")
                print(f"   部位: {row['三级部位']} (编码: {row['部位编码']})")
                print(f"   扫描方式: {row['扫描方式']}")
                print()
        else:
            print("未在MR项目sheet中找到MR水成像MRLH项目")
            
            # 检查所有扫描方式
            print("\n所有MR扫描方式:")
            scan_methods = df_mr['扫描方式'].unique()
            for method in sorted(scan_methods):
                count = len(df_mr[df_mr['扫描方式'] == method])
                print(f"- {method}: {count} 个项目")
        
        # 也检查全部项目sheet
        print("\n=== 检查全部项目sheet ===")
        df_all = pd.read_excel(latest_file, sheet_name='全部项目')
        mrlh_all = df_all[df_all['扫描方式'].str.contains('MRLH', na=False)]
        print(f"全部项目中MR水成像MRLH项目数: {len(mrlh_all)}")
        
        if len(mrlh_all) > 0:
            print("\n全部项目中的MR水成像MRLH项目:")
            for idx, row in mrlh_all.iterrows():
                print(f"{idx+1}. {row['检查项目名称']} - {row['检查项目编码']}")
                print(f"   部位: {row['三级部位']} (编码: {row['部位编码']})")
                print(f"   模态: {row['模态']}")
                print()
        
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    check_output_file()