#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def check_insurance_fields():
    """检查医保项目的字段和数据"""
    try:
        file = 'cli_system/output/完整检查项目清单_20250711_230350.xlsx'
        df = pd.read_excel(file, sheet_name='医保项目')
        
        print("医保项目列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i}. {col}")
        
        print(f"\n总共{len(df.columns)}个字段")
        print(f"总共{len(df)}行数据")
        
        print("\n检查关联部位字段:")
        has_associated_field = '使用关联部位' in df.columns
        print(f"'使用关联部位'字段存在: {has_associated_field}")
        
        if has_associated_field:
            print("\n使用关联部位统计:")
            print(df['使用关联部位'].value_counts())
            
            print("\n关联部位字段详情:")
            if '关联部位' in df.columns:
                print("关联部位字段存在")
                print(df['关联部位'].value_counts())
            
            if '关联部位编码' in df.columns:
                print("关联部位编码字段存在")
                print(df['关联部位编码'].value_counts())
        else:
            print("❌ 关联部位字段不存在，需要检查数据生成逻辑")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_insurance_fields()