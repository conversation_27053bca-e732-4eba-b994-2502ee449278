<template>
  <div class="project-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>检查项目生成管理</h1>
      <p>医疗检查项目的生成、导入和管理</p>
    </div>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" type="card" class="main-tabs">
      <!-- 项目生成 -->
      <el-tab-pane label="项目生成" name="generate">
        <div class="tab-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>生成检查项目</span>
                <el-button type="primary" @click="showGenerateDialog = true">
                  <el-icon><Plus /></el-icon>
                  新建生成任务
                </el-button>
              </div>
            </template>

            <!-- 生成批次列表 -->
            <el-table :data="batches" v-loading="batchesLoading">
              <el-table-column prop="batch_name" label="批次名称" />
              <el-table-column prop="modality" label="模态类型" width="100" />
              <el-table-column prop="generation_rule" label="生成规则" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="total_count" label="总数" width="80" />
              <el-table-column prop="success_count" label="成功" width="80" />
              <el-table-column prop="failed_count" label="失败" width="80" />
              <el-table-column prop="start_time" label="开始时间" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.start_time) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button size="small" @click="viewBatchDetail(row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="batchPage"
                v-model:page-size="batchPageSize"
                :total="batchTotal"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="loadBatches"
                @current-change="loadBatches"
              />
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 数据导入 -->
      <el-tab-pane label="数据导入" name="import">
        <div class="tab-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>Excel数据导入</span>
                <el-button type="primary" @click="showImportDialog = true">
                  <el-icon><Upload /></el-icon>
                  导入数据
                </el-button>
              </div>
            </template>

            <!-- 导入日志列表 -->
            <el-table :data="importLogs" v-loading="logsLoading">
              <el-table-column prop="file_name" label="文件名" />
              <el-table-column prop="table_name" label="目标表" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="total_rows" label="总行数" width="80" />
              <el-table-column prop="success_rows" label="成功" width="80" />
              <el-table-column prop="failed_rows" label="失败" width="80" />
              <el-table-column prop="start_time" label="开始时间" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.start_time) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button size="small" @click="viewImportDetail(row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="logPage"
                v-model:page-size="logPageSize"
                :total="logTotal"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="loadImportLogs"
                @current-change="loadImportLogs"
              />
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 项目列表 -->
      <el-tab-pane label="项目列表" name="projects">
        <div class="tab-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>生成的检查项目</span>
                <div class="header-actions">
                  <el-select v-model="projectModalityFilter" placeholder="模态筛选" clearable @change="loadProjects">
                    <el-option label="CT" value="CT" />
                    <el-option label="MR" value="MR" />
                    <el-option label="DR" value="DR" />
                    <el-option label="MG" value="MG" />
                    <el-option label="RF" value="RF" />
                    <el-option label="IO" value="IO" />
                  </el-select>
                  <el-button @click="exportProjects">
                    <el-icon><Download /></el-icon>
                    导出
                  </el-button>
                </div>
              </div>
            </template>

            <!-- 项目列表 -->
            <el-table :data="projects" v-loading="projectsLoading">
              <el-table-column prop="modality" label="模态" width="80" />
              <el-table-column prop="project_name" label="项目名称" min-width="200" />
              <el-table-column prop="project_code" label="项目编码" width="180" />
              <el-table-column prop="level3_name" label="部位" width="120" />
              <el-table-column prop="insurance_mapping_code" label="医保映射码" width="120" />
              <el-table-column prop="generation_time" label="生成时间" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.generation_time) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button size="small" @click="viewProjectDetail(row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="projectPage"
                v-model:page-size="projectPageSize"
                :total="projectTotal"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="loadProjects"
                @current-change="loadProjects"
              />
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 统计信息 -->
      <el-tab-pane label="统计信息" name="statistics">
        <div class="tab-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card>
                <template #header>
                  <span>项目统计</span>
                </template>
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.projects?.total || 0 }}</div>
                  <div class="stat-label">总项目数</div>
                </div>
                <div class="modality-stats">
                  <div v-for="(count, modality) in statistics.projects?.by_modality" :key="modality" class="modality-item">
                    <span class="modality-name">{{ modality }}</span>
                    <span class="modality-count">{{ count }}</span>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card>
                <template #header>
                  <span>批次统计</span>
                </template>
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.batches?.total || 0 }}</div>
                  <div class="stat-label">总批次数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.batches?.completed || 0 }}</div>
                  <div class="stat-label">已完成</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.batches?.success_rate || '0%' }}</div>
                  <div class="stat-label">成功率</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card>
                <template #header>
                  <span>导入统计</span>
                </template>
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.imports?.total || 0 }}</div>
                  <div class="stat-label">总导入数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.imports?.successful || 0 }}</div>
                  <div class="stat-label">成功导入</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.imports?.success_rate || '0%' }}</div>
                  <div class="stat-label">成功率</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 项目生成对话框 -->
    <ProjectGenerateDialog
      v-model="showGenerateDialog"
      @success="onGenerateSuccess"
    />

    <!-- 数据导入对话框 -->
    <DataImportDialog
      v-model="showImportDialog"
      @success="onImportSuccess"
    />

    <!-- 项目详情对话框 -->
    <ProjectDetailDialog
      v-model="showProjectDetail"
      :project="selectedProject"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Upload, Download } from '@element-plus/icons-vue'
import ProjectGenerateDialog from '@/components/ProjectGenerateDialog.vue'
import DataImportDialog from '@/components/DataImportDialog.vue'
import ProjectDetailDialog from '@/components/ProjectDetailDialog.vue'
import { projectApi } from '@/api/project'

// 响应式数据
const activeTab = ref('generate')
const showGenerateDialog = ref(false)
const showImportDialog = ref(false)
const showProjectDetail = ref(false)
const selectedProject = ref(null)

// 批次数据
const batches = ref([])
const batchesLoading = ref(false)
const batchPage = ref(1)
const batchPageSize = ref(20)
const batchTotal = ref(0)

// 导入日志数据
const importLogs = ref([])
const logsLoading = ref(false)
const logPage = ref(1)
const logPageSize = ref(20)
const logTotal = ref(0)

// 项目数据
const projects = ref([])
const projectsLoading = ref(false)
const projectPage = ref(1)
const projectPageSize = ref(20)
const projectTotal = ref(0)
const projectModalityFilter = ref('')

// 统计数据
const statistics = reactive({
  projects: {},
  batches: {},
  imports: {}
})

// 方法
const loadBatches = async () => {
  batchesLoading.value = true
  try {
    const response = await projectApi.getBatches({
      page: batchPage.value,
      page_size: batchPageSize.value
    })
    batches.value = response.batches
    batchTotal.value = response.total
  } catch (error) {
    ElMessage.error('加载批次列表失败')
  } finally {
    batchesLoading.value = false
  }
}

const loadImportLogs = async () => {
  logsLoading.value = true
  try {
    const response = await projectApi.getImportLogs({
      page: logPage.value,
      page_size: logPageSize.value
    })
    importLogs.value = response.logs
    logTotal.value = response.total
  } catch (error) {
    ElMessage.error('加载导入日志失败')
  } finally {
    logsLoading.value = false
  }
}

const loadProjects = async () => {
  projectsLoading.value = true
  try {
    const response = await projectApi.getProjects({
      modality: projectModalityFilter.value,
      page: projectPage.value,
      page_size: projectPageSize.value
    })
    projects.value = response.projects
    projectTotal.value = response.total
  } catch (error) {
    ElMessage.error('加载项目列表失败')
  } finally {
    projectsLoading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await projectApi.getStatistics()
    Object.assign(statistics, response)
  } catch (error) {
    ElMessage.error('加载统计信息失败')
  }
}

const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const viewBatchDetail = (batch: any) => {
  // 查看批次详情
  console.log('查看批次详情:', batch)
}

const viewImportDetail = (log: any) => {
  // 查看导入详情
  console.log('查看导入详情:', log)
}

const viewProjectDetail = (project: any) => {
  selectedProject.value = project
  showProjectDetail.value = true
}

const exportProjects = () => {
  // 导出项目
  ElMessage.info('导出功能开发中...')
}

const onGenerateSuccess = () => {
  ElMessage.success('项目生成任务已提交')
  loadBatches()
  loadStatistics()
}

const onImportSuccess = () => {
  ElMessage.success('数据导入完成')
  loadImportLogs()
  loadStatistics()
}

// 生命周期
onMounted(() => {
  loadBatches()
  loadImportLogs()
  loadProjects()
  loadStatistics()
})
</script>

<style scoped>
.project-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.main-tabs {
  background: white;
  border-radius: 8px;
}

.tab-content {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.stat-item {
  text-align: center;
  margin-bottom: 16px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.modality-stats {
  margin-top: 16px;
}

.modality-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.modality-item:last-child {
  border-bottom: none;
}

.modality-name {
  font-weight: 500;
}

.modality-count {
  color: #409eff;
  font-weight: bold;
}
</style>
