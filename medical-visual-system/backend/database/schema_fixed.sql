-- =================================================================
-- 医疗检查项目可视化数据处理系统 - 数据库表结构设计
-- =================================================================
-- 版本: v1.0
-- 创建时间: 2025-01-11
-- 数据库: SQLite (开发) / PostgreSQL (生产)
-- 表总数: 10个核心表
-- =================================================================

-- =================================================================
-- 1. 基础数据表
-- =================================================================

-- 1.1 部位结构表 (body_parts)
-- 存储三级医疗部位分类结构数据，包含各模态适用性标记
CREATE TABLE body_parts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level1_code VARCHAR(1) NOT NULL,           -- 一级编码 (1位)
    level1_name VARCHAR(50) NOT NULL,          -- 一级部位名称
    level2_code VARCHAR(2) NOT NULL,           -- 二级编码 (2位)
    level2_name VARCHAR(50) NOT NULL,          -- 二级部位名称
    level3_code VARCHAR(2) NOT NULL,           -- 三级编码 (2位)
    level3_name VARCHAR(100) NOT NULL,         -- 三级部位名称
    part_code VARCHAR(5) NOT NULL UNIQUE,     -- 完整部位编码 (5位: 1+2+2)
    part_code_text VARCHAR(10),               -- 文本格式部位编码（兼容原系统）

    -- 🏥 模态适用性标记 (核心价值字段)
    is_applicable_ct BOOLEAN DEFAULT FALSE,    -- 是否适用CT检查
    is_applicable_mr BOOLEAN DEFAULT FALSE,    -- 是否适用MR检查
    is_applicable_dr BOOLEAN DEFAULT FALSE,    -- 是否适用DR检查
    is_applicable_rf BOOLEAN DEFAULT FALSE,    -- 是否适用RF透视检查
    is_applicable_mg BOOLEAN DEFAULT FALSE,    -- 是否适用MG乳腺钼靶检查
    is_applicable_ot BOOLEAN DEFAULT FALSE,    -- 是否适用其他模态检查

    -- 通用字段
    is_active BOOLEAN DEFAULT TRUE,           -- 是否启用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    UNIQUE(level1_code, level2_code, level3_code)
);

-- 1.2 DR项目清单表 (dr_projects)
-- 存储DR检查项目的详细信息
CREATE TABLE dr_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level1_code VARCHAR(1) NOT NULL,           -- 一级编码
    level1_name VARCHAR(50) NOT NULL,          -- 一级部位
    level2_code VARCHAR(2) NOT NULL,           -- 二级编码
    level2_name VARCHAR(50) NOT NULL,          -- 二级部位
    level3_code VARCHAR(2) NOT NULL,           -- 三级编码
    level3_name VARCHAR(100) NOT NULL,         -- 三级部位
    part_code VARCHAR(5) NOT NULL,             -- 部位编码

    -- DR特有字段
    position VARCHAR(100),                     -- 摆位/摄影体位
    position_code VARCHAR(2),                  -- 摆位编码
    body_position VARCHAR(50),                 -- 体位
    body_position_code VARCHAR(2),             -- 体位编码
    direction VARCHAR(50),                     -- 方向
    direction_code VARCHAR(2),                 -- 方向编码

    -- 分类字段
    population VARCHAR(20),                    -- 人群
    disease VARCHAR(30),                       -- 疾病
    emergency_type VARCHAR(10),                -- 平/急诊

    -- 元数据
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 1.3 医保编码表 (insurance_codes)
-- 存储医保系统的项目编码和名称
CREATE TABLE insurance_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sequence_no VARCHAR(10),                   -- 序号
    category VARCHAR(100),                     -- 归集口径
    insurance_project_code VARCHAR(30),       -- 医保项目码
    insurance_project_name TEXT,              -- 医保项目名称
    mutual_recognition_name TEXT,             -- 互认项目名称
    insurance_mapping_code VARCHAR(6) NOT NULL, -- 医保映射码 (6位)
    insurance_extension_code VARCHAR(2),      -- 医保扩展码 (2位)

    -- 元数据
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    UNIQUE(insurance_mapping_code, insurance_extension_code)
);

-- 1.4 MG项目表 (mg_projects)
-- 存储MG(乳腺钼靶)项目数据
CREATE TABLE mg_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level1_code VARCHAR(1) NOT NULL,           -- 一级编码
    level1_name VARCHAR(50) NOT NULL,          -- 一级部位
    level2_code VARCHAR(2) NOT NULL,           -- 二级编码
    level2_name VARCHAR(50) NOT NULL,          -- 二级部位
    level3_code VARCHAR(2) NOT NULL,           -- 三级编码
    level3_name VARCHAR(100) NOT NULL,         -- 三级部位
    part_code VARCHAR(5) NOT NULL,             -- 部位编码

    -- MG特有字段
    position_tech VARCHAR(100),                -- 摆位/技术
    insurance_mapping_code VARCHAR(6),         -- 医保映射码
    insurance_extension_code VARCHAR(2),       -- 医保扩展码
    project_name TEXT,                         -- 项目名称
    project_code VARCHAR(16),                  -- 项目编码
    population_code VARCHAR(1),                -- 人群编码
    disease_code VARCHAR(1),                   -- 疾病编码
    emergency_code VARCHAR(1),                 -- 平急诊编码
    insurance_project_code VARCHAR(30),        -- 关联医保项目码

    -- 元数据
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =================================================================
-- 2. 数据字典表
-- =================================================================

-- 2.1 模态字典表 (modality_dict)
-- 定义医学影像模态类型
CREATE TABLE modality_dict (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mapping_code_prefix VARCHAR(2) NOT NULL UNIQUE, -- 映射码前缀 (如: 2a, 2b, 3a, 3b, 3c, 11, 13)
    modality_name VARCHAR(10) NOT NULL,             -- 模态名称 (CT/MR/DR/MG)
    modality_desc VARCHAR(100),                     -- 模态描述
    default_mapping BOOLEAN DEFAULT FALSE,          -- 是否为默认映射
    sort_order INTEGER DEFAULT 0,                   -- 排序顺序
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    UNIQUE(modality_name, mapping_code_prefix)
);

-- 2.2 人群字典表 (population_dict)
-- 定义特定人群分类
CREATE TABLE population_dict (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    population_code VARCHAR(1) NOT NULL UNIQUE,    -- 人群编码 (1位)
    population_name VARCHAR(30) NOT NULL,          -- 人群名称
    description TEXT,                               -- 描述说明
    sort_order INTEGER DEFAULT 0,                  -- 排序顺序
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2.3 疾病字典表 (disease_dict)
-- 定义特定疾病分类
CREATE TABLE disease_dict (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    disease_code VARCHAR(1) NOT NULL UNIQUE,       -- 疾病编码 (1位)
    disease_name VARCHAR(30) NOT NULL,             -- 疾病名称
    description TEXT,                               -- 描述说明
    sort_order INTEGER DEFAULT 0,                  -- 排序顺序
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2.4 扫描方式映射表 (scan_mapping)
-- 存储扫描方式与医保编码的映射关系
CREATE TABLE scan_mapping (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scan_method VARCHAR(100) NOT NULL,             -- 扫描方式名称
    scan_classification VARCHAR(50),               -- 扫描分类
    scan_classification_code VARCHAR(2),           -- 扫描分类编码
    scan_code VARCHAR(2),                          -- 扫描编码
    modality VARCHAR(10) NOT NULL,                 -- 所属模态 (CT/MR)
    insurance_mapping_code VARCHAR(6) NOT NULL,    -- 医保映射码
    insurance_extension_code VARCHAR(2),           -- 医保扩展码
    contrast_agent BOOLEAN DEFAULT FALSE,          -- 是否使用对比剂
    applicable_parts TEXT,                         -- 适用部位 (JSON格式)
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    UNIQUE(scan_method, modality)
);

-- =================================================================
-- 3. 项目生成记录表
-- =================================================================

-- 3.1 生成项目表 (generated_projects)
-- 存储系统生成的所有医疗检查项目
CREATE TABLE generated_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,

    -- 基础信息
    modality VARCHAR(10) NOT NULL,                 -- 模态类型 (CT/MR/DR/MG/医保)
    level1_code VARCHAR(1) NOT NULL,               -- 一级编码
    level1_name VARCHAR(50) NOT NULL,              -- 一级部位
    level2_code VARCHAR(2) NOT NULL,               -- 二级编码
    level2_name VARCHAR(50) NOT NULL,              -- 二级部位
    level3_code VARCHAR(2) NOT NULL,               -- 三级编码
    level3_name VARCHAR(100) NOT NULL,             -- 三级部位
    part_code VARCHAR(5) NOT NULL,                 -- 部位编码

    -- 扫描信息 (CT/MR)
    scan_method VARCHAR(100),                      -- 扫描方式
    scan_classification VARCHAR(50),               -- 扫描分类
    scan_classification_code VARCHAR(2),           -- 扫描分类编码
    scan_code VARCHAR(2),                          -- 扫描编码
    contrast_agent BOOLEAN DEFAULT FALSE,          -- 是否使用对比剂

    -- DR特有信息
    position VARCHAR(100),                         -- 摆位(DR)
    position_code VARCHAR(2),                      -- 摆位编码(DR)
    body_position VARCHAR(50),                     -- 体位(DR)
    body_position_code VARCHAR(2),                 -- 体位编码(DR)
    direction VARCHAR(50),                         -- 方向(DR)
    direction_code VARCHAR(2),                     -- 方向编码(DR)

    -- 编码信息
    insurance_mapping_code VARCHAR(6) NOT NULL,    -- 医保映射码
    insurance_extension_code VARCHAR(2),           -- 医保扩展码
    project_name TEXT NOT NULL,                    -- 生成的项目名称
    project_code VARCHAR(16) UNIQUE,               -- 生成的16位编码
    population_code VARCHAR(1) DEFAULT '0',        -- 人群编码
    disease_code VARCHAR(1) DEFAULT '0',           -- 疾病编码
    emergency_code VARCHAR(1) DEFAULT '0',         -- 平急诊编码

    -- 生成信息
    generation_batch_id VARCHAR(50) NOT NULL,      -- 生成批次ID
    source_table VARCHAR(50),                      -- 来源数据表
    generation_rules TEXT,                         -- 生成规则(JSON)

    -- 元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3.2 生成批次记录表 (generation_batches)
-- 记录每次项目生成的批次信息
CREATE TABLE generation_batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id VARCHAR(50) NOT NULL UNIQUE,          -- 批次ID (格式: BATCH_YYYYMMDD_HHMMSS)
    batch_name VARCHAR(100),                       -- 批次名称
    description TEXT,                              -- 批次描述

    -- 生成配置
    modalities TEXT,                               -- 生成的模态列表 (JSON)
    generation_rules TEXT,                         -- 生成规则配置 (JSON)
    source_files TEXT,                             -- 源文件列表 (JSON)

    -- 统计信息
    total_projects INTEGER DEFAULT 0,              -- 总项目数
    ct_projects INTEGER DEFAULT 0,                 -- CT项目数
    mr_projects INTEGER DEFAULT 0,                 -- MR项目数
    dr_projects INTEGER DEFAULT 0,                 -- DR项目数
    mg_projects INTEGER DEFAULT 0,                 -- MG项目数
    insurance_projects INTEGER DEFAULT 0,          -- 医保项目数

    -- 状态信息
    status VARCHAR(20) DEFAULT 'pending',          -- 状态 (pending/running/completed/failed)
    progress INTEGER DEFAULT 0,                    -- 进度百分比 (0-100)
    error_message TEXT,                            -- 错误信息

    -- 时间信息
    started_at TIMESTAMP,                          -- 开始时间
    completed_at TIMESTAMP,                        -- 完成时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =================================================================
-- 4. 索引定义
-- =================================================================

-- 部位结构表索引
CREATE INDEX idx_body_parts_part_code ON body_parts(part_code);
CREATE INDEX idx_body_parts_level1 ON body_parts(level1_code);
CREATE INDEX idx_body_parts_level2 ON body_parts(level2_code);
CREATE INDEX idx_body_parts_level3 ON body_parts(level3_code);
CREATE INDEX idx_body_parts_ct ON body_parts(is_applicable_ct);
CREATE INDEX idx_body_parts_mr ON body_parts(is_applicable_mr);
CREATE INDEX idx_body_parts_dr ON body_parts(is_applicable_dr);

-- DR项目表索引
CREATE INDEX idx_dr_projects_part_code ON dr_projects(part_code);
CREATE INDEX idx_dr_projects_position ON dr_projects(position);
CREATE INDEX idx_dr_projects_population ON dr_projects(population);

-- 医保编码表索引
CREATE INDEX idx_insurance_codes_mapping ON insurance_codes(insurance_mapping_code);
CREATE INDEX idx_insurance_codes_project_code ON insurance_codes(insurance_project_code);

-- MG项目表索引
CREATE INDEX idx_mg_projects_part_code ON mg_projects(part_code);
CREATE INDEX idx_mg_projects_project_code ON mg_projects(project_code);
CREATE INDEX idx_mg_projects_insurance_mapping ON mg_projects(insurance_mapping_code);

-- 模态字典表索引
CREATE INDEX idx_modality_dict_name ON modality_dict(modality_name);

-- 人群字典表索引
CREATE INDEX idx_population_dict_name ON population_dict(population_name);

-- 疾病字典表索引
CREATE INDEX idx_disease_dict_name ON disease_dict(disease_name);

-- 扫描方式映射表索引
CREATE INDEX idx_scan_mapping_modality ON scan_mapping(modality);
CREATE INDEX idx_scan_mapping_code ON scan_mapping(insurance_mapping_code, insurance_extension_code);

-- 生成项目表索引
CREATE INDEX idx_generated_projects_modality ON generated_projects(modality);
CREATE INDEX idx_generated_projects_batch ON generated_projects(generation_batch_id);
CREATE INDEX idx_generated_projects_insurance_mapping ON generated_projects(insurance_mapping_code);
CREATE INDEX idx_generated_projects_part_code ON generated_projects(part_code);
CREATE INDEX idx_generated_projects_created ON generated_projects(created_at);

-- 生成批次表索引
CREATE INDEX idx_generation_batches_status ON generation_batches(status);
CREATE INDEX idx_generation_batches_created ON generation_batches(created_at);
CREATE INDEX idx_generation_batches_completed ON generation_batches(completed_at);

-- =================================================================
-- 5. 基础数据插入
-- =================================================================

-- 插入默认模态字典数据
INSERT INTO modality_dict (mapping_code_prefix, modality_name, modality_desc, default_mapping, sort_order) VALUES
('22', 'CT', 'CT计算机断层扫描', 1, 1),
('23', 'MR', 'MR磁共振成像', 1, 2),
('21', 'DR', 'DR数字化X线摄影', 1, 3),
('24', 'MG', 'MG乳腺钼靶摄影', 1, 4);

-- 插入默认人群字典数据
INSERT INTO population_dict (population_code, population_name, description, sort_order) VALUES
('0', '通用人群', '适用于所有人群的检查项目', 1),
('1', '儿童', '适用于儿童的检查项目', 2),
('2', '孕妇', '适用于孕妇的检查项目', 3),
('3', '老年人', '适用于老年人的检查项目', 4);

-- 插入默认疾病字典数据
INSERT INTO disease_dict (disease_code, disease_name, description, sort_order) VALUES
('0', '通用疾病', '适用于所有疾病的检查项目', 1),
('1', '肿瘤', '肿瘤相关检查项目', 2),
('2', '外伤', '外伤相关检查项目', 3),
('3', '感染', '感染相关检查项目', 4);