-- =================================================================
-- 医疗检查项目可视化数据处理系统 - 数据库表结构设计
-- =================================================================
-- 版本: v1.0
-- 创建时间: 2025-01-11
-- 数据库: SQLite (开发) / PostgreSQL (生产)
-- 表总数: 10个核心表
-- =================================================================

-- =================================================================
-- 1. 基础数据表
-- =================================================================

-- 1.1 部位结构表 (body_parts)
-- 存储三级医疗部位分类结构数据，包含各模态适用性标记
CREATE TABLE body_parts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level1_code VARCHAR(1) NOT NULL,           -- 一级编码 (1位)
    level1_name VARCHAR(50) NOT NULL,          -- 一级部位名称
    level2_code VARCHAR(2) NOT NULL,           -- 二级编码 (2位)
    level2_name VARCHAR(50) NOT NULL,          -- 二级部位名称
    level3_code VARCHAR(2) NOT NULL,           -- 三级编码 (2位)
    level3_name VARCHAR(100) NOT NULL,         -- 三级部位名称
    part_code VARCHAR(5) NOT NULL UNIQUE,     -- 完整部位编码 (5位: 1+2+2)
    part_code_text VARCHAR(10),               -- 文本格式部位编码（兼容原系统）

    -- 🏥 模态适用性标记 (核心价值字段)
    is_applicable_ct BOOLEAN DEFAULT FALSE,    -- 是否适用CT检查
    is_applicable_mr BOOLEAN DEFAULT FALSE,    -- 是否适用MR检查
    is_applicable_dr BOOLEAN DEFAULT FALSE,    -- 是否适用DR检查
    is_applicable_rf BOOLEAN DEFAULT FALSE,    -- 是否适用RF透视检查
    is_applicable_mg BOOLEAN DEFAULT FALSE,    -- 是否适用MG乳腺钼靶检查
    is_applicable_ot BOOLEAN DEFAULT FALSE,    -- 是否适用其他模态检查

    -- 通用字段
    is_active BOOLEAN DEFAULT TRUE,           -- 是否启用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    UNIQUE(level1_code, level2_code, level3_code)
);

-- 1.2 DR项目清单表 (dr_projects)
-- 存储DR检查项目的详细信息
CREATE TABLE dr_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level1_code VARCHAR(1) NOT NULL,           -- 一级编码
    level1_name VARCHAR(50) NOT NULL,          -- 一级部位
    level2_code VARCHAR(2) NOT NULL,           -- 二级编码
    level2_name VARCHAR(50) NOT NULL,          -- 二级部位
    level3_code VARCHAR(2) NOT NULL,           -- 三级编码
    level3_name VARCHAR(100) NOT NULL,         -- 三级部位
    part_code VARCHAR(5) NOT NULL,             -- 部位编码

    -- DR特有字段
    position VARCHAR(100),                     -- 摆位/摄影体位
    position_code VARCHAR(2),                  -- 摆位编码
    body_position VARCHAR(50),                 -- 体位
    body_position_code VARCHAR(2),             -- 体位编码
    direction VARCHAR(50),                     -- 方向
    direction_code VARCHAR(2),                 -- 方向编码

    -- 分类字段
    population VARCHAR(20),                    -- 人群
    disease VARCHAR(30),                       -- 疾病
    emergency_type VARCHAR(10),                -- 平/急诊

    -- 元数据
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 1.3 医保编码表 (insurance_codes)
-- 存储医保系统的项目编码和名称
CREATE TABLE insurance_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sequence_no VARCHAR(10),                   -- 序号
    category VARCHAR(100),                     -- 归集口径
    insurance_project_code VARCHAR(30),       -- 医保项目码
    insurance_project_name TEXT,              -- 医保项目名称
    mutual_recognition_name TEXT,             -- 互认项目名称
    insurance_mapping_code VARCHAR(6) NOT NULL, -- 医保映射码 (6位)
    insurance_extension_code VARCHAR(2),      -- 医保扩展码 (2位)

    -- 元数据
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    UNIQUE(insurance_mapping_code, insurance_extension_code)
);

-- 1.4 MG项目表 (mg_projects)
-- 存储MG(乳腺钼靶)项目数据
CREATE TABLE mg_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level1_code VARCHAR(1) NOT NULL,           -- 一级编码
    level1_name VARCHAR(50) NOT NULL,          -- 一级部位
    level2_code VARCHAR(2) NOT NULL,           -- 二级编码
    level2_name VARCHAR(50) NOT NULL,          -- 二级部位
    level3_code VARCHAR(2) NOT NULL,           -- 三级编码
    level3_name VARCHAR(100) NOT NULL,         -- 三级部位
    part_code VARCHAR(5) NOT NULL,             -- 部位编码

    -- MG特有字段
    position_tech VARCHAR(100),                -- 摆位/技术
    insurance_mapping_code VARCHAR(6),         -- 医保映射码
    insurance_extension_code VARCHAR(2),       -- 医保扩展码
    project_name TEXT,                         -- 项目名称
    project_code VARCHAR(16),                  -- 项目编码
    population_code VARCHAR(1),                -- 人群编码
    disease_code VARCHAR(1),                   -- 疾病编码
    emergency_code VARCHAR(1),                 -- 平急诊编码
    insurance_project_code VARCHAR(30),        -- 关联医保项目码

    -- 元数据
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_mg_part_code (part_code),
    INDEX idx_mg_project_code (project_code),
    INDEX idx_mg_insurance_mapping (insurance_mapping_code)
);

-- =================================================================
-- 2. 数据字典表
-- =================================================================

-- 2.1 模态字典表 (modality_dict)
-- 定义医学影像模态类型
CREATE TABLE modality_dict (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mapping_code_prefix VARCHAR(2) NOT NULL UNIQUE, -- 映射码前缀 (如: 2a, 2b, 3a, 3b, 3c, 11, 13)
    modality_name VARCHAR(10) NOT NULL,             -- 模态名称 (CT/MR/DR/MG)
    modality_desc VARCHAR(100),                     -- 模态描述
    default_mapping BOOLEAN DEFAULT FALSE,          -- 是否为默认映射
    sort_order INTEGER DEFAULT 0,                   -- 排序顺序
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    UNIQUE(modality_name, mapping_code_prefix),
    INDEX idx_modality_name (modality_name)
);

-- 2.2 人群字典表 (population_dict)
-- 定义特定人群分类
CREATE TABLE population_dict (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    population_code VARCHAR(1) NOT NULL UNIQUE,    -- 人群编码 (1位)
    population_name VARCHAR(30) NOT NULL,          -- 人群名称
    description TEXT,                               -- 描述说明
    sort_order INTEGER DEFAULT 0,                  -- 排序顺序
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_population_name (population_name)
);

-- 2.3 疾病字典表 (disease_dict)
-- 定义特定疾病分类
CREATE TABLE disease_dict (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    disease_code VARCHAR(1) NOT NULL UNIQUE,       -- 疾病编码 (1位)
    disease_name VARCHAR(30) NOT NULL,             -- 疾病名称
    description TEXT,                               -- 描述说明
    sort_order INTEGER DEFAULT 0,                  -- 排序顺序
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_disease_name (disease_name)
);

-- 2.4 扫描方式映射表 (scan_mapping)
-- 定义CT/MR扫描方式与医保编码的映射关系
CREATE TABLE scan_mapping (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scan_method VARCHAR(150) NOT NULL,             -- 扫描方式描述
    scan_method_short VARCHAR(50),                 -- 扫描方式简称
    insurance_mapping_code VARCHAR(6) NOT NULL,    -- 医保映射码
    insurance_extension_code VARCHAR(2) NOT NULL,  -- 医保扩展码
    modality VARCHAR(10) NOT NULL,                 -- 适用模态 (CT/MR)
    applicable_parts TEXT,                          -- 适用部位 (JSON格式)
    contrast_agent BOOLEAN DEFAULT FALSE,          -- 是否使用对比剂
    sort_order INTEGER DEFAULT 0,                  -- 排序顺序
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    UNIQUE(scan_method, modality),
    INDEX idx_scan_modality (modality),
    INDEX idx_scan_mapping_code (insurance_mapping_code, insurance_extension_code)
);

-- =================================================================
-- 3. 项目生成记录表
-- =================================================================

-- 3.1 生成项目表 (generated_projects)
-- 存储系统生成的所有医疗检查项目
CREATE TABLE generated_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,

    -- 基础信息
    modality VARCHAR(10) NOT NULL,                 -- 模态 (CT/MR/DR/MG/医保)
    level1_code VARCHAR(1) NOT NULL,               -- 一级编码
    level1_name VARCHAR(50) NOT NULL,              -- 一级部位
    level2_code VARCHAR(2) NOT NULL,               -- 二级编码
    level2_name VARCHAR(50) NOT NULL,              -- 二级部位
    level3_code VARCHAR(2) NOT NULL,               -- 三级编码
    level3_name VARCHAR(100) NOT NULL,             -- 三级部位
    part_code VARCHAR(5) NOT NULL,                 -- 部位编码

    -- 特定字段（根据模态不同而不同）
    scan_method VARCHAR(150),                       -- 扫描方式 (CT/MR)
    position VARCHAR(100),                          -- 摆位/摄影体位 (DR)
    position_code VARCHAR(2),                       -- 摆位编码 (DR)
    body_position VARCHAR(50),                      -- 体位 (DR)
    body_position_code VARCHAR(2),                  -- 体位编码 (DR)
    direction VARCHAR(50),                          -- 方向 (DR)
    direction_code VARCHAR(2),                      -- 方向编码 (DR)

    -- 编码信息
    insurance_mapping_code VARCHAR(6) NOT NULL,    -- 医保映射码
    insurance_extension_code VARCHAR(2),           -- 医保扩展码
    project_name TEXT NOT NULL,                    -- 生成的项目名称
    project_code VARCHAR(16) NOT NULL,             -- 生成的16位编码
    population_code VARCHAR(1) DEFAULT '0',        -- 人群编码
    disease_code VARCHAR(1) DEFAULT '0',           -- 疾病编码
    emergency_code VARCHAR(1) DEFAULT '0',         -- 平急诊编码

    -- 生成信息
    generation_batch_id VARCHAR(50) NOT NULL,      -- 生成批次ID
    source_table VARCHAR(50),                      -- 来源数据表
    generation_rules TEXT,                         -- 生成规则 (JSON)

    -- 元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    UNIQUE(project_code),
    INDEX idx_gen_modality (modality),
    INDEX idx_gen_batch (generation_batch_id),
    INDEX idx_gen_insurance_mapping (insurance_mapping_code),
    INDEX idx_gen_part_code (part_code),
    INDEX idx_gen_created (created_at)
);

-- 3.2 生成批次记录表 (generation_batches)
-- 记录每次项目生成的批次信息
CREATE TABLE generation_batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id VARCHAR(50) NOT NULL UNIQUE,          -- 批次ID (格式: BATCH_YYYYMMDD_HHMMSS)
    batch_name VARCHAR(150),                        -- 批次名称
    batch_desc TEXT,                                -- 批次描述

    -- 统计信息
    total_projects INTEGER DEFAULT 0,              -- 总项目数
    ct_count INTEGER DEFAULT 0,                    -- CT项目数
    mr_count INTEGER DEFAULT 0,                    -- MR项目数
    dr_count INTEGER DEFAULT 0,                    -- DR项目数
    mg_count INTEGER DEFAULT 0,                    -- MG项目数
    insurance_count INTEGER DEFAULT 0,             -- 医保项目数

    -- 状态信息
    status VARCHAR(20) DEFAULT 'pending',          -- 状态: pending/processing/completed/failed
    progress_percent INTEGER DEFAULT 0,            -- 完成百分比
    error_count INTEGER DEFAULT 0,                 -- 错误数量
    error_details TEXT,                             -- 错误详情 (JSON)

    -- 配置信息
    generation_config TEXT,                        -- 生成配置 (JSON)
    data_source_info TEXT,                         -- 数据源信息 (JSON)

    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,

    -- 索引
    INDEX idx_batch_status (status),
    INDEX idx_batch_created (created_at),
    INDEX idx_batch_completed (completed_at)
);

-- =================================================================
-- 4. 初始化基础数据
-- =================================================================

-- 4.1 初始化模态字典
INSERT INTO modality_dict (mapping_code_prefix, modality_name, modality_desc, default_mapping, sort_order) VALUES
('2a', 'CT', 'X线计算机体层摄影', TRUE, 1),
('2b', 'CT', 'X线计算机体层摄影(特殊)', FALSE, 2),
('3a', 'MR', '磁共振成像', TRUE, 3),
('3b', 'MR', '磁共振成像(增强)', FALSE, 4),
('3c', 'MR', '磁共振成像(特殊)', FALSE, 5),
('11', 'DR', 'X线数字化摄影', TRUE, 6),
('13', 'MG', '乳腺钼靶摄影', TRUE, 7);

-- 4.2 初始化人群字典
INSERT INTO population_dict (population_code, population_name, description, sort_order) VALUES
('0', '一般人群', '普通患者群体', 0),
('1', '胎儿', '胎儿检查专用', 1),
('2', '新生儿', '新生儿检查专用', 2),
('3', '儿童', '儿童检查专用', 3),
('4', '孕妇', '孕妇检查专用', 4);

-- 4.3 初始化疾病字典
INSERT INTO disease_dict (disease_code, disease_name, description, sort_order) VALUES
('0', '一般疾病', '常规检查项目', 0),
('1', '外伤', '外伤相关检查', 1),
('2', '认知', '认知功能检查', 2),
('3', '癫痫', '癫痫相关检查', 3),
('4', 'PICC', 'PICC相关检查', 4),
('5', '卒中', '脑卒中相关检查', 5),
('6', '胸痛', '胸痛相关检查', 6);

-- =================================================================
-- 5. 触发器和约束
-- =================================================================

-- 5.1 更新时间戳触发器（SQLite版本）
-- 当记录更新时自动更新 updated_at 字段

-- body_parts 更新触发器
CREATE TRIGGER update_body_parts_timestamp
    AFTER UPDATE ON body_parts
    FOR EACH ROW
    WHEN OLD.updated_at = NEW.updated_at OR OLD.updated_at IS NULL
BEGIN
    UPDATE body_parts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- dr_projects 更新触发器
CREATE TRIGGER update_dr_projects_timestamp
    AFTER UPDATE ON dr_projects
    FOR EACH ROW
    WHEN OLD.updated_at = NEW.updated_at OR OLD.updated_at IS NULL
BEGIN
    UPDATE dr_projects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- insurance_codes 更新触发器
CREATE TRIGGER update_insurance_codes_timestamp
    AFTER UPDATE ON insurance_codes
    FOR EACH ROW
    WHEN OLD.updated_at = NEW.updated_at OR OLD.updated_at IS NULL
BEGIN
    UPDATE insurance_codes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- mg_projects 更新触发器
CREATE TRIGGER update_mg_projects_timestamp
    AFTER UPDATE ON mg_projects
    FOR EACH ROW
    WHEN OLD.updated_at = NEW.updated_at OR OLD.updated_at IS NULL
BEGIN
    UPDATE mg_projects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- modality_dict 更新触发器
CREATE TRIGGER update_modality_dict_timestamp
    AFTER UPDATE ON modality_dict
    FOR EACH ROW
    WHEN OLD.updated_at = NEW.updated_at OR OLD.updated_at IS NULL
BEGIN
    UPDATE modality_dict SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- population_dict 更新触发器
CREATE TRIGGER update_population_dict_timestamp
    AFTER UPDATE ON population_dict
    FOR EACH ROW
    WHEN OLD.updated_at = NEW.updated_at OR OLD.updated_at IS NULL
BEGIN
    UPDATE population_dict SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- disease_dict 更新触发器
CREATE TRIGGER update_disease_dict_timestamp
    AFTER UPDATE ON disease_dict
    FOR EACH ROW
    WHEN OLD.updated_at = NEW.updated_at OR OLD.updated_at IS NULL
BEGIN
    UPDATE disease_dict SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- scan_mapping 更新触发器
CREATE TRIGGER update_scan_mapping_timestamp
    AFTER UPDATE ON scan_mapping
    FOR EACH ROW
    WHEN OLD.updated_at = NEW.updated_at OR OLD.updated_at IS NULL
BEGIN
    UPDATE scan_mapping SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- =================================================================
-- 6. 视图定义
-- =================================================================

-- 6.1 完整项目视图 - 用于项目生成概览
CREATE VIEW v_project_overview AS
SELECT
    modality,
    level1_name,
    level2_name,
    level3_name,
    COUNT(*) as project_count,
    MAX(created_at) as last_generated
FROM generated_projects
GROUP BY modality, level1_name, level2_name, level3_name
ORDER BY modality, level1_name, level2_name, level3_name;

-- 6.2 批次统计视图
CREATE VIEW v_batch_statistics AS
SELECT
    DATE(created_at) as generation_date,
    COUNT(*) as batch_count,
    SUM(total_projects) as total_projects,
    SUM(ct_count) as total_ct,
    SUM(mr_count) as total_mr,
    SUM(dr_count) as total_dr,
    SUM(mg_count) as total_mg,
    SUM(insurance_count) as total_insurance,
    AVG(progress_percent) as avg_progress
FROM generation_batches
GROUP BY DATE(created_at)
ORDER BY generation_date DESC;

-- 6.3 数据质量检查视图
CREATE VIEW v_data_quality_check AS
SELECT
    'body_parts' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_records,
    COUNT(CASE WHEN level1_code IS NULL OR level1_code = '' THEN 1 END) as missing_level1,
    COUNT(CASE WHEN part_code IS NULL OR part_code = '' THEN 1 END) as missing_part_code
FROM body_parts
UNION ALL
SELECT
    'dr_projects' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_records,
    COUNT(CASE WHEN position IS NULL OR position = '' THEN 1 END) as missing_position,
    COUNT(CASE WHEN part_code IS NULL OR part_code = '' THEN 1 END) as missing_part_code
FROM dr_projects
UNION ALL
SELECT
    'insurance_codes' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_records,
    COUNT(CASE WHEN insurance_mapping_code IS NULL OR insurance_mapping_code = '' THEN 1 END) as missing_mapping_code,
    COUNT(CASE WHEN insurance_project_name IS NULL OR insurance_project_name = '' THEN 1 END) as missing_project_name
FROM insurance_codes;

-- =================================================================
-- 数据库设计完成
-- =================================================================
--
-- 特性总结:
-- ✅ 10个核心数据表覆盖所有业务需求
-- ✅ 完整的索引设计优化查询性能
-- ✅ 自动时间戳更新机制
-- ✅ 基础数据字典初始化
-- ✅ 业务视图便于数据分析
-- ✅ 数据质量检查机制
-- ✅ 兼容 SQLite 和 PostgreSQL
--
-- 下一步: 创建数据库迁移文件和初始化脚本
-- =================================================================