#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目管理相关的Pydantic模型
创建时间: 2025-01-11
版本: v1.0
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class GenerationType(str, Enum):
    """生成类型枚举"""
    CT_MR = "CT_MR"
    DR = "DR"
    INSURANCE = "INSURANCE"
    MG = "MG"
    ALL = "ALL"


class ProjectGenerationRequest(BaseModel):
    """项目生成请求"""
    batch_name: str = Field(..., description="批次名称")
    generation_type: GenerationType = Field(..., description="生成类型")
    modality: Optional[str] = Field(None, description="模态类型")
    config: Optional[Dict[str, Any]] = Field(None, description="生成配置")
    
    class Config:
        schema_extra = {
            "example": {
                "batch_name": "CT/MR项目生成_20250111",
                "generation_type": "CT_MR",
                "modality": "CT",
                "config": {
                    "include_scan_methods": ["平扫", "增强"],
                    "exclude_parts": []
                }
            }
        }


class ProjectGenerationResponse(BaseModel):
    """项目生成响应"""
    batch_id: str = Field(..., description="批次ID")
    status: str = Field(..., description="状态")
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    message: str = Field(..., description="消息")
    
    class Config:
        schema_extra = {
            "example": {
                "batch_id": "batch_20250111_143022_a1b2c3d4",
                "status": "completed",
                "total_count": 1500,
                "success_count": 1500,
                "failed_count": 0,
                "message": "项目生成完成，共生成1500个项目"
            }
        }


class DataImportRequest(BaseModel):
    """数据导入请求"""
    sheet_mappings: Dict[str, str] = Field(..., description="Sheet名称到表名的映射")
    
    class Config:
        schema_extra = {
            "example": {
                "sheet_mappings": {
                    "三级部位": "body_parts",
                    "医保编码": "insurance_codes",
                    "扫描方式": "scan_mappings"
                }
            }
        }


class DataImportResponse(BaseModel):
    """数据导入响应"""
    import_id: str = Field(..., description="导入ID")
    status: str = Field(..., description="状态")
    total_rows: int = Field(..., description="总行数")
    success_rows: int = Field(..., description="成功行数")
    failed_rows: int = Field(..., description="失败行数")
    results: Dict[str, Any] = Field(..., description="详细结果")
    message: str = Field(..., description="消息")
    
    class Config:
        schema_extra = {
            "example": {
                "import_id": "import_20250111_143022_a1b2c3d4",
                "status": "completed",
                "total_rows": 500,
                "success_rows": 495,
                "failed_rows": 5,
                "results": {
                    "三级部位": {"success": 129, "failed": 0},
                    "医保编码": {"success": 117, "failed": 0},
                    "扫描方式": {"success": 249, "failed": 5}
                },
                "message": "数据导入完成，成功495行，失败5行"
            }
        }


class ProjectBase(BaseModel):
    """项目基础信息"""
    modality: str = Field(..., description="模态")
    project_name: str = Field(..., description="项目名称")
    project_code: str = Field(..., description="项目编码")
    level1_name: Optional[str] = Field(None, description="一级部位")
    level2_name: Optional[str] = Field(None, description="二级部位")
    level3_name: Optional[str] = Field(None, description="三级部位")
    part_code: Optional[str] = Field(None, description="部位编码")
    insurance_mapping_code: Optional[str] = Field(None, description="医保映射编码")


class ProjectListItem(ProjectBase):
    """项目列表项"""
    id: int = Field(..., description="项目ID")
    generation_batch_id: Optional[str] = Field(None, description="生成批次ID")
    generation_time: Optional[datetime] = Field(None, description="生成时间")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        orm_mode = True


class ProjectListResponse(BaseModel):
    """项目列表响应"""
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="页码")
    page_size: int = Field(..., description="每页数量")
    projects: List[ProjectListItem] = Field(..., description="项目列表")
    
    class Config:
        schema_extra = {
            "example": {
                "total": 1500,
                "page": 1,
                "page_size": 20,
                "projects": [
                    {
                        "id": 1,
                        "modality": "CT",
                        "project_name": "CT头部(平扫)",
                        "project_code": "2100000101000000",
                        "level1_name": "头颈部",
                        "level2_name": "头部",
                        "level3_name": "头部",
                        "part_code": "01010",
                        "insurance_mapping_code": "210000",
                        "generation_batch_id": "batch_20250111_143022_a1b2c3d4",
                        "generation_time": "2025-01-11T14:30:22",
                        "created_at": "2025-01-11T14:30:22"
                    }
                ]
            }
        }


class ProjectDetailResponse(BaseModel):
    """项目详情响应"""
    id: int = Field(..., description="项目ID")
    modality: str = Field(..., description="模态")
    project_name: str = Field(..., description="项目名称")
    project_code: str = Field(..., description="项目编码")
    
    # 部位信息
    level1_code: Optional[str] = Field(None, description="一级编码")
    level1_name: Optional[str] = Field(None, description="一级部位")
    level2_code: Optional[str] = Field(None, description="二级编码")
    level2_name: Optional[str] = Field(None, description="二级部位")
    level3_code: Optional[str] = Field(None, description="三级编码")
    level3_name: Optional[str] = Field(None, description="三级部位")
    part_code: Optional[str] = Field(None, description="部位编码")
    
    # 医保信息
    insurance_mapping_code: Optional[str] = Field(None, description="医保映射编码")
    insurance_project_name: Optional[str] = Field(None, description="医保项目名称")
    insurance_extension_code: Optional[str] = Field(None, description="医保扩展码")
    insurance_project_code: Optional[str] = Field(None, description="医保项目码")
    
    # 扫描信息
    scan_method: Optional[str] = Field(None, description="扫描方式")
    scan_code: Optional[str] = Field(None, description="扫描编码")
    
    # DR特有信息
    position: Optional[str] = Field(None, description="摆位")
    position_code: Optional[str] = Field(None, description="摆位码")
    
    # 编码信息
    population_code: Optional[str] = Field(None, description="人群编码")
    disease_code: Optional[str] = Field(None, description="疾病编码")
    emergency_code: Optional[str] = Field(None, description="平急诊编码")
    
    # 生成信息
    generation_batch_id: Optional[str] = Field(None, description="生成批次ID")
    generation_rule: Optional[str] = Field(None, description="生成规则")
    generation_time: Optional[datetime] = Field(None, description="生成时间")
    
    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        orm_mode = True
        schema_extra = {
            "example": {
                "id": 1,
                "modality": "CT",
                "project_name": "CT头部(平扫)",
                "project_code": "2100000101000000",
                "level1_code": "01",
                "level1_name": "头颈部",
                "level2_code": "01",
                "level2_name": "头部",
                "level3_code": "00",
                "level3_name": "头部",
                "part_code": "01010",
                "insurance_mapping_code": "210000",
                "insurance_project_name": "CT平扫",
                "insurance_extension_code": "00",
                "insurance_project_code": "012301010010000",
                "scan_method": "CT-平扫",
                "scan_code": "01",
                "position": None,
                "position_code": None,
                "population_code": "0",
                "disease_code": "0",
                "emergency_code": "0",
                "generation_batch_id": "batch_20250111_143022_a1b2c3d4",
                "generation_rule": "CT_MR_STANDARD",
                "generation_time": "2025-01-11T14:30:22",
                "created_at": "2025-01-11T14:30:22",
                "updated_at": "2025-01-11T14:30:22"
            }
        }


class BatchBase(BaseModel):
    """批次基础信息"""
    batch_name: str = Field(..., description="批次名称")
    modality: Optional[str] = Field(None, description="模态类型")
    generation_rule: Optional[str] = Field(None, description="生成规则")


class BatchListItem(BatchBase):
    """批次列表项"""
    id: int = Field(..., description="批次ID")
    batch_id: str = Field(..., description="批次唯一ID")
    status: str = Field(..., description="状态")
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    class Config:
        orm_mode = True


class ImportLogBase(BaseModel):
    """导入日志基础信息"""
    file_name: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")
    sheet_name: Optional[str] = Field(None, description="Sheet名称")
    table_name: Optional[str] = Field(None, description="目标表名")


class ImportLogListItem(ImportLogBase):
    """导入日志列表项"""
    id: int = Field(..., description="日志ID")
    import_id: str = Field(..., description="导入唯一ID")
    status: str = Field(..., description="状态")
    total_rows: int = Field(..., description="总行数")
    success_rows: int = Field(..., description="成功行数")
    failed_rows: int = Field(..., description="失败行数")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    class Config:
        orm_mode = True


class StatisticsResponse(BaseModel):
    """统计信息响应"""
    projects: Dict[str, Any] = Field(..., description="项目统计")
    batches: Dict[str, Any] = Field(..., description="批次统计")
    imports: Dict[str, Any] = Field(..., description="导入统计")
    
    class Config:
        schema_extra = {
            "example": {
                "projects": {
                    "total": 1500,
                    "by_modality": {
                        "CT": 600,
                        "MR": 500,
                        "DR": 200,
                        "MG": 100,
                        "RF": 100
                    }
                },
                "batches": {
                    "total": 10,
                    "completed": 9,
                    "success_rate": "90.0%"
                },
                "imports": {
                    "total": 5,
                    "successful": 5,
                    "success_rate": "100.0%"
                }
            }
        }
