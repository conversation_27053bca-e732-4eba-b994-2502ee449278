#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入服务
创建时间: 2025-01-11
版本: v1.0
"""

import uuid
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text
import logging
import json

from app.models.medical_models import (
    BodyPart, DrProject, InsuranceCode, MgProject, ScanMapping,
    Modality, Population, Disease, DataImportLog
)

logger = logging.getLogger(__name__)


class DataImportService:
    """数据导入服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.import_id = None
        self.validation_errors = []
    
    def create_import_log(self, file_name: str, file_size: int, sheet_name: str = None, 
                         table_name: str = None) -> str:
        """
        创建导入日志
        
        Args:
            file_name: 文件名
            file_size: 文件大小
            sheet_name: Sheet名称
            table_name: 目标表名
            
        Returns:
            导入ID
        """
        import_id = f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        
        log = DataImportLog(
            import_id=import_id,
            file_name=file_name,
            file_size=file_size,
            sheet_name=sheet_name,
            table_name=table_name,
            status='pending',
            start_time=datetime.now()
        )
        
        self.db.add(log)
        self.db.commit()
        
        self.import_id = import_id
        self.validation_errors = []
        
        logger.info(f"创建导入日志: {import_id}")
        return import_id
    
    def update_import_status(self, status: str, total_rows: int = 0, 
                           success_rows: int = 0, failed_rows: int = 0,
                           error_message: str = None):
        """更新导入状态"""
        if not self.import_id:
            return
        
        log = self.db.query(DataImportLog).filter(
            DataImportLog.import_id == self.import_id
        ).first()
        
        if log:
            log.status = status
            log.total_rows = total_rows
            log.success_rows = success_rows
            log.failed_rows = failed_rows
            if error_message:
                log.error_message = error_message
            if status in ['completed', 'failed']:
                log.end_time = datetime.now()
            if self.validation_errors:
                log.validation_errors = json.dumps(self.validation_errors, ensure_ascii=False)
            
            self.db.commit()
    
    def import_excel_file(self, file_path: str, sheet_mappings: Dict[str, str]) -> Dict[str, Any]:
        """
        导入Excel文件
        
        Args:
            file_path: Excel文件路径
            sheet_mappings: Sheet名称到表名的映射
            
        Returns:
            导入结果统计
        """
        results = {}
        
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            
            for sheet_name, table_name in sheet_mappings.items():
                if sheet_name in excel_file.sheet_names:
                    logger.info(f"开始导入Sheet: {sheet_name} -> {table_name}")
                    
                    # 读取数据
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    # 根据表名选择导入方法
                    if table_name == 'body_parts':
                        result = self._import_body_parts(df)
                    elif table_name == 'dr_projects':
                        result = self._import_dr_projects(df)
                    elif table_name == 'insurance_codes':
                        result = self._import_insurance_codes(df)
                    elif table_name == 'mg_projects':
                        result = self._import_mg_projects(df)
                    elif table_name == 'scan_mappings':
                        result = self._import_scan_mappings(df)
                    elif table_name == 'modalities':
                        result = self._import_modalities(df)
                    else:
                        result = {'success': 0, 'failed': len(df), 'error': f'未知表名: {table_name}'}
                    
                    results[sheet_name] = result
                    logger.info(f"Sheet {sheet_name} 导入完成: {result}")
                else:
                    results[sheet_name] = {'success': 0, 'failed': 0, 'error': f'Sheet不存在: {sheet_name}'}
            
            return results
            
        except Exception as e:
            logger.error(f"导入Excel文件失败: {e}")
            raise
    
    def _import_body_parts(self, df: pd.DataFrame) -> Dict[str, Any]:
        """导入部位数据"""
        success_count = 0
        failed_count = 0
        
        try:
            for index, row in df.iterrows():
                try:
                    # 数据验证
                    if pd.isna(row.get('部位编码')):
                        self.validation_errors.append(f"第{index+2}行: 部位编码不能为空")
                        failed_count += 1
                        continue
                    
                    # 检查是否已存在
                    existing = self.db.query(BodyPart).filter(
                        BodyPart.part_code == str(row['部位编码'])
                    ).first()
                    
                    if existing:
                        # 更新现有记录
                        existing.level1_code = str(row.get('一级编码', ''))
                        existing.level1_name = str(row.get('一级部位', ''))
                        existing.level2_code = str(row.get('二级编码', ''))
                        existing.level2_name = str(row.get('二级部位', ''))
                        existing.level3_code = str(row.get('三级编码', ''))
                        existing.level3_name = str(row.get('三级部位', ''))
                        existing.ct_applicable = bool(row.get('CT', False))
                        existing.mr_applicable = bool(row.get('MR', False))
                        existing.dr_applicable = bool(row.get('DR', False))
                    else:
                        # 创建新记录
                        body_part = BodyPart(
                            level1_code=str(row.get('一级编码', '')),
                            level1_name=str(row.get('一级部位', '')),
                            level2_code=str(row.get('二级编码', '')),
                            level2_name=str(row.get('二级部位', '')),
                            level3_code=str(row.get('三级编码', '')),
                            level3_name=str(row.get('三级部位', '')),
                            part_code=str(row['部位编码']),
                            ct_applicable=bool(row.get('CT', False)),
                            mr_applicable=bool(row.get('MR', False)),
                            dr_applicable=bool(row.get('DR', False))
                        )
                        self.db.add(body_part)
                    
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"导入部位数据第{index+2}行失败: {e}")
                    self.validation_errors.append(f"第{index+2}行: {str(e)}")
                    failed_count += 1
            
            self.db.commit()
            return {'success': success_count, 'failed': failed_count}
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"导入部位数据失败: {e}")
            return {'success': 0, 'failed': len(df), 'error': str(e)}
    
    def _import_dr_projects(self, df: pd.DataFrame) -> Dict[str, Any]:
        """导入DR项目数据"""
        success_count = 0
        failed_count = 0
        
        try:
            for index, row in df.iterrows():
                try:
                    # 数据验证
                    if pd.isna(row.get('部位编码')):
                        self.validation_errors.append(f"第{index+2}行: 部位编码不能为空")
                        failed_count += 1
                        continue
                    
                    # 创建或更新DR项目
                    dr_project = DrProject(
                        level1_code=str(row.get('一级编码', '')),
                        level1_name=str(row.get('一级部位', '')),
                        level2_code=str(row.get('二级编码', '')),
                        level2_name=str(row.get('二级部位', '')),
                        level3_code=str(row.get('三级编码', '')),
                        level3_name=str(row.get('三级部位', '')),
                        part_code=str(row['部位编码']),
                        position=str(row.get('摆位', '')),
                        body_position=str(row.get('体位', '')),
                        direction=str(row.get('方向', '')),
                        position_code=str(row.get('摆位码', '')),
                        population=str(row.get('人群', '')),
                        disease=str(row.get('疾病', '')),
                        emergency_type=str(row.get('平急诊', ''))
                    )
                    
                    self.db.add(dr_project)
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"导入DR项目第{index+2}行失败: {e}")
                    self.validation_errors.append(f"第{index+2}行: {str(e)}")
                    failed_count += 1
            
            self.db.commit()
            return {'success': success_count, 'failed': failed_count}
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"导入DR项目失败: {e}")
            return {'success': 0, 'failed': len(df), 'error': str(e)}
    
    def _import_insurance_codes(self, df: pd.DataFrame) -> Dict[str, Any]:
        """导入医保编码数据"""
        success_count = 0
        failed_count = 0
        
        try:
            for index, row in df.iterrows():
                try:
                    # 数据验证
                    if pd.isna(row.get('医保项目码')):
                        self.validation_errors.append(f"第{index+2}行: 医保项目码不能为空")
                        failed_count += 1
                        continue
                    
                    # 检查是否已存在
                    existing = self.db.query(InsuranceCode).filter(
                        InsuranceCode.insurance_project_code == str(row['医保项目码'])
                    ).first()
                    
                    if existing:
                        # 更新现有记录
                        existing.sequence_number = str(row.get('序号', ''))
                        existing.collection_scope = str(row.get('归集口径', ''))
                        existing.insurance_mapping_code = str(row.get('医保映射码', ''))
                        existing.insurance_project_name = str(row.get('医保项目名称', ''))
                        existing.insurance_extension_code = str(row.get('医保扩展码', ''))
                        existing.mutual_recognition_name = str(row.get('互认项目名称', ''))
                        existing.remarks = str(row.get('备注', ''))
                    else:
                        # 创建新记录
                        insurance_code = InsuranceCode(
                            sequence_number=str(row.get('序号', '')),
                            collection_scope=str(row.get('归集口径', '')),
                            insurance_project_code=str(row['医保项目码']),
                            insurance_mapping_code=str(row.get('医保映射码', '')),
                            insurance_project_name=str(row.get('医保项目名称', '')),
                            insurance_extension_code=str(row.get('医保扩展码', '')),
                            mutual_recognition_name=str(row.get('互认项目名称', '')),
                            remarks=str(row.get('备注', ''))
                        )
                        self.db.add(insurance_code)
                    
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"导入医保编码第{index+2}行失败: {e}")
                    self.validation_errors.append(f"第{index+2}行: {str(e)}")
                    failed_count += 1
            
            self.db.commit()
            return {'success': success_count, 'failed': failed_count}
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"导入医保编码失败: {e}")
            return {'success': 0, 'failed': len(df), 'error': str(e)}
    
    def _import_scan_mappings(self, df: pd.DataFrame) -> Dict[str, Any]:
        """导入扫描方式映射数据"""
        success_count = 0
        failed_count = 0
        
        try:
            for index, row in df.iterrows():
                try:
                    # 创建扫描映射记录
                    scan_mapping = ScanMapping(
                        scan_method=str(row.get('扫描方式', '')),
                        insurance_mapping_code=str(row.get('医保映射码', '')),
                        insurance_extension_code=str(row.get('医保扩展码', '')),
                        scan_classification_code=str(row.get('扫描分类编码', '')),
                        scan_classification_name=str(row.get('扫描分类名称', '')),
                        scan_code=str(row.get('扫描编码', '')),
                        scan_name=str(row.get('扫描名称', ''))
                    )
                    
                    self.db.add(scan_mapping)
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"导入扫描映射第{index+2}行失败: {e}")
                    self.validation_errors.append(f"第{index+2}行: {str(e)}")
                    failed_count += 1
            
            self.db.commit()
            return {'success': success_count, 'failed': failed_count}
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"导入扫描映射失败: {e}")
            return {'success': 0, 'failed': len(df), 'error': str(e)}
    
    def _import_mg_projects(self, df: pd.DataFrame) -> Dict[str, Any]:
        """导入MG项目数据"""
        success_count = 0
        failed_count = 0
        
        try:
            for index, row in df.iterrows():
                try:
                    # 创建MG项目记录
                    mg_project = MgProject(
                        project_name=str(row.get('项目名称', '')),
                        project_code=str(row.get('项目编码', '')),
                        description=str(row.get('项目描述', '')),
                        insurance_mapping_code=str(row.get('医保映射码', '')),
                        insurance_extension_code=str(row.get('医保扩展码', ''))
                    )
                    
                    self.db.add(mg_project)
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"导入MG项目第{index+2}行失败: {e}")
                    self.validation_errors.append(f"第{index+2}行: {str(e)}")
                    failed_count += 1
            
            self.db.commit()
            return {'success': success_count, 'failed': failed_count}
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"导入MG项目失败: {e}")
            return {'success': 0, 'failed': len(df), 'error': str(e)}
    
    def _import_modalities(self, df: pd.DataFrame) -> Dict[str, Any]:
        """导入模态数据"""
        success_count = 0
        failed_count = 0
        
        try:
            for index, row in df.iterrows():
                try:
                    # 检查是否已存在
                    existing = self.db.query(Modality).filter(
                        Modality.code == str(row.get('模态编码', ''))
                    ).first()
                    
                    if existing:
                        # 更新现有记录
                        existing.name = str(row.get('模态名称', ''))
                        existing.description = str(row.get('模态描述', ''))
                    else:
                        # 创建新记录
                        modality = Modality(
                            code=str(row.get('模态编码', '')),
                            name=str(row.get('模态名称', '')),
                            description=str(row.get('模态描述', '')),
                            sort_order=int(row.get('排序', 0))
                        )
                        self.db.add(modality)
                    
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"导入模态第{index+2}行失败: {e}")
                    self.validation_errors.append(f"第{index+2}行: {str(e)}")
                    failed_count += 1
            
            self.db.commit()
            return {'success': success_count, 'failed': failed_count}
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"导入模态失败: {e}")
            return {'success': 0, 'failed': len(df), 'error': str(e)}
