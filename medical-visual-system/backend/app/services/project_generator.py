#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目生成引擎
创建时间: 2025-01-11
版本: v1.0
"""

import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.medical_models import (
    BodyPart, DrProject, InsuranceCode, MgProject, ScanMapping,
    GeneratedProject, GenerationBatch, Modality
)
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class ProjectGenerationEngine:
    """医疗检查项目生成引擎"""
    
    def __init__(self, db: Session):
        self.db = db
        self.batch_id = None
        self.generation_config = {}
    
    def create_generation_batch(self, batch_name: str, modality: str = None, 
                              generation_rule: str = None, config: Dict = None) -> str:
        """
        创建生成批次
        
        Args:
            batch_name: 批次名称
            modality: 模态类型
            generation_rule: 生成规则
            config: 生成配置
            
        Returns:
            批次ID
        """
        batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        
        batch = GenerationBatch(
            batch_id=batch_id,
            batch_name=batch_name,
            modality=modality,
            generation_rule=generation_rule,
            status='pending',
            start_time=datetime.now(),
            generation_config=str(config) if config else None
        )
        
        self.db.add(batch)
        self.db.commit()
        
        self.batch_id = batch_id
        self.generation_config = config or {}
        
        logger.info(f"创建生成批次: {batch_id}")
        return batch_id
    
    def update_batch_status(self, status: str, total_count: int = 0, 
                          success_count: int = 0, failed_count: int = 0,
                          error_message: str = None):
        """更新批次状态"""
        if not self.batch_id:
            return
        
        batch = self.db.query(GenerationBatch).filter(
            GenerationBatch.batch_id == self.batch_id
        ).first()
        
        if batch:
            batch.status = status
            batch.total_count = total_count
            batch.success_count = success_count
            batch.failed_count = failed_count
            if error_message:
                batch.error_message = error_message
            if status in ['completed', 'failed']:
                batch.end_time = datetime.now()
            
            self.db.commit()
    
    def generate_ct_mr_projects(self) -> List[Dict[str, Any]]:
        """
        生成CT/MR检查项目
        
        Returns:
            生成的项目列表
        """
        logger.info("开始生成CT/MR项目")
        projects = []
        
        try:
            # 获取所有适用于CT/MR的部位
            body_parts = self.db.query(BodyPart).filter(
                or_(BodyPart.ct_applicable == True, BodyPart.mr_applicable == True),
                BodyPart.is_active == True
            ).all()
            
            # 获取扫描方式映射
            scan_mappings = self.db.query(ScanMapping).filter(
                ScanMapping.is_active == True
            ).all()
            
            for part in body_parts:
                for scan in scan_mappings:
                    # 确定模态
                    modality = self._determine_modality_from_scan(scan.insurance_mapping_code)
                    
                    # 检查部位是否适用于该模态
                    if modality == 'CT' and not part.ct_applicable:
                        continue
                    if modality == 'MR' and not part.mr_applicable:
                        continue
                    
                    # 生成项目编码
                    project_code = self._generate_project_code(
                        insurance_code=scan.insurance_mapping_code,
                        part_code=part.part_code,
                        extension_code=scan.insurance_extension_code or '00'
                    )
                    
                    # 生成项目名称
                    project_name = self._generate_project_name(
                        modality=modality,
                        part_name=part.level3_name,
                        scan_method=scan.scan_method
                    )
                    
                    project = {
                        'modality': modality,
                        'project_name': project_name,
                        'project_code': project_code,
                        'level1_code': part.level1_code,
                        'level1_name': part.level1_name,
                        'level2_code': part.level2_code,
                        'level2_name': part.level2_name,
                        'level3_code': part.level3_code,
                        'level3_name': part.level3_name,
                        'part_code': part.part_code,
                        'insurance_mapping_code': scan.insurance_mapping_code,
                        'insurance_extension_code': scan.insurance_extension_code or '00',
                        'scan_method': scan.scan_method,
                        'scan_code': scan.scan_code,
                        'population_code': '0',
                        'disease_code': '0',
                        'emergency_code': '0',
                        'generation_batch_id': self.batch_id,
                        'generation_rule': 'CT_MR_STANDARD'
                    }
                    
                    projects.append(project)
            
            logger.info(f"生成CT/MR项目完成，共{len(projects)}个")
            return projects
            
        except Exception as e:
            logger.error(f"生成CT/MR项目失败: {e}")
            raise
    
    def generate_dr_projects(self) -> List[Dict[str, Any]]:
        """
        生成DR检查项目
        
        Returns:
            生成的项目列表
        """
        logger.info("开始生成DR项目")
        projects = []
        
        try:
            # 获取所有DR项目
            dr_projects = self.db.query(DrProject).filter(
                DrProject.is_active == True
            ).all()
            
            for dr_project in dr_projects:
                # 生成项目编码
                project_code = self._generate_dr_project_code(
                    part_code=dr_project.part_code,
                    position_code=dr_project.position_code or '00'
                )
                
                # 生成项目名称
                project_name = f"DR{dr_project.level3_name}-{dr_project.position}"
                
                project = {
                    'modality': 'DR',
                    'project_name': project_name,
                    'project_code': project_code,
                    'level1_code': dr_project.level1_code,
                    'level1_name': dr_project.level1_name,
                    'level2_code': dr_project.level2_code,
                    'level2_name': dr_project.level2_name,
                    'level3_code': dr_project.level3_code,
                    'level3_name': dr_project.level3_name,
                    'part_code': dr_project.part_code,
                    'insurance_mapping_code': '110000',  # DR固定映射码
                    'insurance_extension_code': '00',
                    'position': dr_project.position,
                    'position_code': dr_project.position_code or '00',
                    'population_code': '0',
                    'disease_code': '0',
                    'emergency_code': '0',
                    'generation_batch_id': self.batch_id,
                    'generation_rule': 'DR_STANDARD'
                }
                
                projects.append(project)
            
            logger.info(f"生成DR项目完成，共{len(projects)}个")
            return projects
            
        except Exception as e:
            logger.error(f"生成DR项目失败: {e}")
            raise
    
    def generate_insurance_projects(self) -> List[Dict[str, Any]]:
        """
        生成医保检查项目（无部位）
        
        Returns:
            生成的项目列表
        """
        logger.info("开始生成医保项目")
        projects = []
        
        try:
            # 获取所有医保编码
            insurance_codes = self.db.query(InsuranceCode).filter(
                InsuranceCode.is_active == True
            ).all()
            
            for insurance in insurance_codes:
                # 确定模态
                modality = self._determine_modality_from_insurance(insurance.insurance_mapping_code)
                
                # 生成项目编码
                project_code = self._generate_insurance_project_code(
                    insurance_code=insurance.insurance_mapping_code,
                    extension_code=insurance.insurance_extension_code or '00'
                )
                
                # 使用互认项目名称作为项目名称
                project_name = insurance.mutual_recognition_name or insurance.insurance_project_name
                
                project = {
                    'modality': modality,
                    'project_name': project_name,
                    'project_code': project_code,
                    'level1_code': '9',  # 其他类别
                    'level1_name': '其他',
                    'level2_code': '02',  # 其他子类
                    'level2_name': '其他',
                    'level3_code': '00',  # 其他具体部位
                    'level3_name': '其他',
                    'part_code': '90200',  # 5位部位编码
                    'insurance_mapping_code': insurance.insurance_mapping_code,
                    'insurance_project_name': insurance.insurance_project_name,
                    'insurance_extension_code': insurance.insurance_extension_code or '00',
                    'insurance_project_code': insurance.insurance_project_code,
                    'population_code': '0',
                    'disease_code': '0',
                    'emergency_code': '0',
                    'generation_batch_id': self.batch_id,
                    'generation_rule': 'INSURANCE_STANDARD'
                }
                
                projects.append(project)
            
            logger.info(f"生成医保项目完成，共{len(projects)}个")
            return projects
            
        except Exception as e:
            logger.error(f"生成医保项目失败: {e}")
            raise
    
    def generate_mg_projects(self) -> List[Dict[str, Any]]:
        """
        生成MG检查项目
        
        Returns:
            生成的项目列表
        """
        logger.info("开始生成MG项目")
        projects = []
        
        try:
            # 获取所有MG项目
            mg_projects = self.db.query(MgProject).filter(
                MgProject.is_active == True
            ).all()
            
            for mg_project in mg_projects:
                # 生成项目编码
                project_code = self._generate_mg_project_code(
                    insurance_code=mg_project.insurance_mapping_code or '130100',
                    extension_code=mg_project.insurance_extension_code or '00'
                )
                
                project = {
                    'modality': 'MG',
                    'project_name': mg_project.project_name,
                    'project_code': project_code,
                    'level1_code': '9',  # 其他类别
                    'level1_name': '其他',
                    'level2_code': '02',  # 其他子类
                    'level2_name': '其他',
                    'level3_code': '00',  # 其他具体部位
                    'level3_name': '其他',
                    'part_code': '90200',  # 5位部位编码
                    'insurance_mapping_code': mg_project.insurance_mapping_code or '130100',
                    'insurance_extension_code': mg_project.insurance_extension_code or '00',
                    'population_code': '0',
                    'disease_code': '0',
                    'emergency_code': '0',
                    'generation_batch_id': self.batch_id,
                    'generation_rule': 'MG_STANDARD'
                }
                
                projects.append(project)
            
            logger.info(f"生成MG项目完成，共{len(projects)}个")
            return projects
            
        except Exception as e:
            logger.error(f"生成MG项目失败: {e}")
            raise
    
    def save_generated_projects(self, projects: List[Dict[str, Any]]) -> Tuple[int, int]:
        """
        保存生成的项目到数据库
        
        Args:
            projects: 项目列表
            
        Returns:
            (成功数量, 失败数量)
        """
        success_count = 0
        failed_count = 0
        
        for project_data in projects:
            try:
                # 检查是否已存在相同编码的项目
                existing = self.db.query(GeneratedProject).filter(
                    GeneratedProject.project_code == project_data['project_code']
                ).first()
                
                if existing:
                    # 更新现有项目
                    for key, value in project_data.items():
                        if hasattr(existing, key):
                            setattr(existing, key, value)
                else:
                    # 创建新项目
                    project = GeneratedProject(**project_data)
                    self.db.add(project)
                
                success_count += 1
                
            except Exception as e:
                logger.error(f"保存项目失败: {project_data.get('project_code', 'unknown')} - {e}")
                failed_count += 1
        
        try:
            self.db.commit()
            logger.info(f"项目保存完成: 成功{success_count}, 失败{failed_count}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"批量保存失败: {e}")
            failed_count = len(projects)
            success_count = 0
        
        return success_count, failed_count
    
    def _determine_modality_from_scan(self, insurance_mapping_code: str) -> str:
        """根据医保映射码确定模态"""
        if not insurance_mapping_code:
            return 'Unknown'
        
        first_digit = insurance_mapping_code[0] if len(insurance_mapping_code) > 0 else ''
        
        if first_digit == '2':
            return 'CT'
        elif first_digit == '3':
            return 'MR'
        elif first_digit == '1':
            if len(insurance_mapping_code) >= 2:
                first_two = insurance_mapping_code[:2]
                mapping = {
                    '11': 'DR',
                    '12': 'IO',
                    '13': 'MG',
                    '14': 'RF'
                }
                return mapping.get(first_two, 'Unknown')
        
        return 'Unknown'
    
    def _determine_modality_from_insurance(self, insurance_mapping_code: str) -> str:
        """根据医保映射码确定模态（医保项目专用）"""
        return self._determine_modality_from_scan(insurance_mapping_code)
    
    def _generate_project_code(self, insurance_code: str, part_code: str, extension_code: str = '00') -> str:
        """生成16位项目编码"""
        # 格式：[医保映射码6位][部位编码5位][医保扩展码2位][人群编码1位][疾病编码1位][平急诊编码1位]
        insurance_code_6 = str(insurance_code).ljust(6, '0')[:6]
        part_code_5 = str(part_code).ljust(5, '0')[:5]
        extension_code_2 = str(extension_code).zfill(2)[:2]
        
        return insurance_code_6 + part_code_5 + extension_code_2 + '000'
    
    def _generate_dr_project_code(self, part_code: str, position_code: str = '00') -> str:
        """生成DR项目编码"""
        return self._generate_project_code('110000', part_code, position_code)
    
    def _generate_insurance_project_code(self, insurance_code: str, extension_code: str = '00') -> str:
        """生成医保项目编码（无部位）"""
        return self._generate_project_code(insurance_code, '90200', extension_code)
    
    def _generate_mg_project_code(self, insurance_code: str, extension_code: str = '00') -> str:
        """生成MG项目编码"""
        return self._generate_project_code(insurance_code, '90200', extension_code)
    
    def _generate_project_name(self, modality: str, part_name: str, scan_method: str = None) -> str:
        """生成项目名称"""
        if scan_method:
            # 去除扫描方式前缀
            scan_simple = scan_method.replace('CT-', '').replace('MR-', '')
            return f"{modality}{part_name}({scan_simple})"
        else:
            return f"{modality}{part_name}"
