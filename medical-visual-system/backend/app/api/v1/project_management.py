#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查项目管理API路由
创建时间: 2025-01-11
版本: v1.0
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import tempfile
import os
import json

from app.core.database import get_db
from app.services.project_generator import ProjectGenerationEngine
from app.services.data_import import DataImportService
from app.models.medical_models import (
    GeneratedProject, GenerationBatch, DataImportLog,
    BodyPart, DrProject, InsuranceCode, MgProject, ScanMapping
)
from app.schemas.project_schemas import (
    ProjectGenerationRequest, ProjectGenerationResponse,
    DataImportRequest, DataImportResponse,
    ProjectListResponse, ProjectDetailResponse
)

router = APIRouter(prefix="/project-management", tags=["项目管理"])


@router.post("/generate", response_model=ProjectGenerationResponse)
async def generate_projects(
    request: ProjectGenerationRequest,
    db: Session = Depends(get_db)
):
    """
    生成检查项目
    
    支持的生成类型：
    - CT_MR: CT/MR项目
    - DR: DR项目  
    - INSURANCE: 医保项目
    - MG: MG项目
    - ALL: 所有类型
    """
    try:
        # 创建项目生成引擎
        generator = ProjectGenerationEngine(db)
        
        # 创建生成批次
        batch_id = generator.create_generation_batch(
            batch_name=request.batch_name,
            modality=request.modality,
            generation_rule=request.generation_type,
            config=request.config
        )
        
        # 更新批次状态为运行中
        generator.update_batch_status('running')
        
        all_projects = []
        total_count = 0
        success_count = 0
        failed_count = 0
        
        try:
            # 根据生成类型执行相应的生成逻辑
            if request.generation_type in ['CT_MR', 'ALL']:
                ct_mr_projects = generator.generate_ct_mr_projects()
                all_projects.extend(ct_mr_projects)
            
            if request.generation_type in ['DR', 'ALL']:
                dr_projects = generator.generate_dr_projects()
                all_projects.extend(dr_projects)
            
            if request.generation_type in ['INSURANCE', 'ALL']:
                insurance_projects = generator.generate_insurance_projects()
                all_projects.extend(insurance_projects)
            
            if request.generation_type in ['MG', 'ALL']:
                mg_projects = generator.generate_mg_projects()
                all_projects.extend(mg_projects)
            
            total_count = len(all_projects)
            
            # 保存生成的项目
            if all_projects:
                success_count, failed_count = generator.save_generated_projects(all_projects)
            
            # 更新批次状态为完成
            generator.update_batch_status(
                'completed', 
                total_count=total_count,
                success_count=success_count,
                failed_count=failed_count
            )
            
            return ProjectGenerationResponse(
                batch_id=batch_id,
                status='completed',
                total_count=total_count,
                success_count=success_count,
                failed_count=failed_count,
                message=f"项目生成完成，共生成{success_count}个项目"
            )
            
        except Exception as e:
            # 更新批次状态为失败
            generator.update_batch_status('failed', error_message=str(e))
            raise HTTPException(status_code=500, detail=f"项目生成失败: {str(e)}")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成请求处理失败: {str(e)}")


@router.post("/import-data", response_model=DataImportResponse)
async def import_data(
    file: UploadFile = File(...),
    sheet_mappings: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    导入Excel数据
    
    sheet_mappings格式: {"Sheet名称": "表名"}
    支持的表名:
    - body_parts: 部位数据
    - dr_projects: DR项目数据
    - insurance_codes: 医保编码数据
    - mg_projects: MG项目数据
    - scan_mappings: 扫描方式映射数据
    - modalities: 模态数据
    """
    try:
        # 解析sheet映射
        try:
            mappings = json.loads(sheet_mappings)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="sheet_mappings格式错误")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        try:
            # 创建数据导入服务
            import_service = DataImportService(db)
            
            # 创建导入日志
            import_id = import_service.create_import_log(
                file_name=file.filename,
                file_size=len(content)
            )
            
            # 更新导入状态为运行中
            import_service.update_import_status('running')
            
            # 执行导入
            results = import_service.import_excel_file(tmp_file_path, mappings)
            
            # 计算总体统计
            total_success = sum(r.get('success', 0) for r in results.values())
            total_failed = sum(r.get('failed', 0) for r in results.values())
            total_rows = total_success + total_failed
            
            # 更新导入状态为完成
            import_service.update_import_status(
                'completed',
                total_rows=total_rows,
                success_rows=total_success,
                failed_rows=total_failed
            )
            
            return DataImportResponse(
                import_id=import_id,
                status='completed',
                total_rows=total_rows,
                success_rows=total_success,
                failed_rows=total_failed,
                results=results,
                message=f"数据导入完成，成功{total_success}行，失败{total_failed}行"
            )
            
        except Exception as e:
            # 更新导入状态为失败
            import_service.update_import_status('failed', error_message=str(e))
            raise HTTPException(status_code=500, detail=f"数据导入失败: {str(e)}")
            
        finally:
            # 清理临时文件
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)
                
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入请求处理失败: {str(e)}")


@router.get("/projects", response_model=ProjectListResponse)
async def get_projects(
    modality: Optional[str] = Query(None, description="模态筛选"),
    batch_id: Optional[str] = Query(None, description="批次ID筛选"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取生成的项目列表"""
    try:
        # 构建查询
        query = db.query(GeneratedProject).filter(GeneratedProject.is_active == True)
        
        if modality:
            query = query.filter(GeneratedProject.modality == modality)
        
        if batch_id:
            query = query.filter(GeneratedProject.generation_batch_id == batch_id)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        projects = query.offset(offset).limit(page_size).all()
        
        # 转换为响应格式
        project_list = []
        for project in projects:
            project_dict = {
                'id': project.id,
                'modality': project.modality,
                'project_name': project.project_name,
                'project_code': project.project_code,
                'level1_name': project.level1_name,
                'level2_name': project.level2_name,
                'level3_name': project.level3_name,
                'part_code': project.part_code,
                'insurance_mapping_code': project.insurance_mapping_code,
                'generation_batch_id': project.generation_batch_id,
                'generation_time': project.generation_time,
                'created_at': project.created_at
            }
            project_list.append(project_dict)
        
        return ProjectListResponse(
            total=total,
            page=page,
            page_size=page_size,
            projects=project_list
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")


@router.get("/projects/{project_id}", response_model=ProjectDetailResponse)
async def get_project_detail(
    project_id: int,
    db: Session = Depends(get_db)
):
    """获取项目详情"""
    try:
        project = db.query(GeneratedProject).filter(
            GeneratedProject.id == project_id,
            GeneratedProject.is_active == True
        ).first()
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 转换为响应格式
        project_detail = {
            'id': project.id,
            'modality': project.modality,
            'project_name': project.project_name,
            'project_code': project.project_code,
            'level1_code': project.level1_code,
            'level1_name': project.level1_name,
            'level2_code': project.level2_code,
            'level2_name': project.level2_name,
            'level3_code': project.level3_code,
            'level3_name': project.level3_name,
            'part_code': project.part_code,
            'insurance_mapping_code': project.insurance_mapping_code,
            'insurance_project_name': project.insurance_project_name,
            'insurance_extension_code': project.insurance_extension_code,
            'insurance_project_code': project.insurance_project_code,
            'scan_method': project.scan_method,
            'scan_code': project.scan_code,
            'position': project.position,
            'position_code': project.position_code,
            'population_code': project.population_code,
            'disease_code': project.disease_code,
            'emergency_code': project.emergency_code,
            'generation_batch_id': project.generation_batch_id,
            'generation_rule': project.generation_rule,
            'generation_time': project.generation_time,
            'created_at': project.created_at,
            'updated_at': project.updated_at
        }
        
        return ProjectDetailResponse(**project_detail)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目详情失败: {str(e)}")


@router.get("/batches")
async def get_generation_batches(
    status: Optional[str] = Query(None, description="状态筛选"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取生成批次列表"""
    try:
        # 构建查询
        query = db.query(GenerationBatch)
        
        if status:
            query = query.filter(GenerationBatch.status == status)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        batches = query.order_by(GenerationBatch.start_time.desc()).offset(offset).limit(page_size).all()
        
        # 转换为响应格式
        batch_list = []
        for batch in batches:
            batch_dict = {
                'id': batch.id,
                'batch_id': batch.batch_id,
                'batch_name': batch.batch_name,
                'modality': batch.modality,
                'generation_rule': batch.generation_rule,
                'status': batch.status,
                'total_count': batch.total_count,
                'success_count': batch.success_count,
                'failed_count': batch.failed_count,
                'start_time': batch.start_time,
                'end_time': batch.end_time,
                'error_message': batch.error_message
            }
            batch_list.append(batch_dict)
        
        return {
            'total': total,
            'page': page,
            'page_size': page_size,
            'batches': batch_list
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取批次列表失败: {str(e)}")


@router.get("/import-logs")
async def get_import_logs(
    status: Optional[str] = Query(None, description="状态筛选"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取导入日志列表"""
    try:
        # 构建查询
        query = db.query(DataImportLog)
        
        if status:
            query = query.filter(DataImportLog.status == status)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        logs = query.order_by(DataImportLog.start_time.desc()).offset(offset).limit(page_size).all()
        
        # 转换为响应格式
        log_list = []
        for log in logs:
            log_dict = {
                'id': log.id,
                'import_id': log.import_id,
                'file_name': log.file_name,
                'file_size': log.file_size,
                'sheet_name': log.sheet_name,
                'table_name': log.table_name,
                'status': log.status,
                'total_rows': log.total_rows,
                'success_rows': log.success_rows,
                'failed_rows': log.failed_rows,
                'start_time': log.start_time,
                'end_time': log.end_time,
                'error_message': log.error_message
            }
            log_list.append(log_dict)
        
        return {
            'total': total,
            'page': page,
            'page_size': page_size,
            'logs': log_list
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取导入日志失败: {str(e)}")


@router.get("/statistics")
async def get_statistics(db: Session = Depends(get_db)):
    """获取统计信息"""
    try:
        # 项目统计
        total_projects = db.query(GeneratedProject).filter(GeneratedProject.is_active == True).count()
        
        # 按模态统计
        modality_stats = db.query(
            GeneratedProject.modality,
            db.func.count(GeneratedProject.id).label('count')
        ).filter(
            GeneratedProject.is_active == True
        ).group_by(GeneratedProject.modality).all()
        
        # 批次统计
        total_batches = db.query(GenerationBatch).count()
        completed_batches = db.query(GenerationBatch).filter(GenerationBatch.status == 'completed').count()
        
        # 导入统计
        total_imports = db.query(DataImportLog).count()
        successful_imports = db.query(DataImportLog).filter(DataImportLog.status == 'completed').count()
        
        return {
            'projects': {
                'total': total_projects,
                'by_modality': {stat.modality: stat.count for stat in modality_stats}
            },
            'batches': {
                'total': total_batches,
                'completed': completed_batches,
                'success_rate': f"{completed_batches/total_batches*100:.1f}%" if total_batches > 0 else "0%"
            },
            'imports': {
                'total': total_imports,
                'successful': successful_imports,
                'success_rate': f"{successful_imports/total_imports*100:.1f}%" if total_imports > 0 else "0%"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
