#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目数据模型
创建时间: 2025-01-11
版本: v1.0
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.models.base import BaseModel, Base


class Modality(BaseModel):
    """模态字典表"""
    __tablename__ = "modalities"
    
    code = Column(String(10), unique=True, nullable=False, comment="模态编码")
    name = Column(String(50), nullable=False, comment="模态名称")
    description = Column(Text, comment="模态描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    # 索引
    __table_args__ = (
        Index('idx_modality_code', 'code'),
        Index('idx_modality_active', 'is_active'),
    )


class Population(BaseModel):
    """人群字典表"""
    __tablename__ = "populations"
    
    code = Column(String(10), unique=True, nullable=False, comment="人群编码")
    name = Column(String(50), nullable=False, comment="人群名称")
    description = Column(Text, comment="人群描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    sort_order = Column(Integer, default=0, comment="排序顺序")


class Disease(BaseModel):
    """疾病字典表"""
    __tablename__ = "diseases"
    
    code = Column(String(10), unique=True, nullable=False, comment="疾病编码")
    name = Column(String(100), nullable=False, comment="疾病名称")
    description = Column(Text, comment="疾病描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    sort_order = Column(Integer, default=0, comment="排序顺序")


class ScanMapping(BaseModel):
    """扫描方式医保映射表"""
    __tablename__ = "scan_mappings"
    
    scan_method = Column(String(100), nullable=False, comment="扫描方式")
    insurance_mapping_code = Column(String(20), nullable=False, comment="医保映射码")
    insurance_extension_code = Column(String(10), comment="医保扩展码")
    scan_classification_code = Column(String(10), comment="扫描分类编码")
    scan_classification_name = Column(String(50), comment="扫描分类名称")
    scan_code = Column(String(10), comment="扫描编码")
    scan_name = Column(String(100), comment="扫描名称")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 索引
    __table_args__ = (
        Index('idx_scan_mapping_code', 'insurance_mapping_code'),
        Index('idx_scan_method', 'scan_method'),
    )


class BodyPart(BaseModel):
    """部位结构表"""
    __tablename__ = "body_parts"
    
    level1_code = Column(String(10), nullable=False, comment="一级编码")
    level1_name = Column(String(50), nullable=False, comment="一级部位")
    level2_code = Column(String(10), nullable=False, comment="二级编码")
    level2_name = Column(String(50), nullable=False, comment="二级部位")
    level3_code = Column(String(10), nullable=False, comment="三级编码")
    level3_name = Column(String(100), nullable=False, comment="三级部位")
    part_code = Column(String(20), unique=True, nullable=False, comment="部位编码")
    
    # CT/MR适用性
    ct_applicable = Column(Boolean, default=False, comment="CT适用")
    mr_applicable = Column(Boolean, default=False, comment="MR适用")
    dr_applicable = Column(Boolean, default=False, comment="DR适用")
    
    # 扫描方式适用性（动态列，根据实际扫描方式添加）
    # 这些字段会在数据导入时动态创建
    
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 索引
    __table_args__ = (
        Index('idx_part_code', 'part_code'),
        Index('idx_level1', 'level1_code'),
        Index('idx_level2', 'level2_code'),
        Index('idx_level3', 'level3_code'),
        Index('idx_ct_applicable', 'ct_applicable'),
        Index('idx_mr_applicable', 'mr_applicable'),
    )


class DrProject(BaseModel):
    """DR项目表"""
    __tablename__ = "dr_projects"
    
    level1_code = Column(String(10), nullable=False, comment="一级编码")
    level1_name = Column(String(50), nullable=False, comment="一级部位")
    level2_code = Column(String(10), nullable=False, comment="二级编码")
    level2_name = Column(String(50), nullable=False, comment="二级部位")
    level3_code = Column(String(10), nullable=False, comment="三级编码")
    level3_name = Column(String(100), nullable=False, comment="三级部位")
    part_code = Column(String(20), nullable=False, comment="部位编码")
    
    position = Column(String(100), comment="摆位")
    body_position = Column(String(50), comment="体位")
    direction = Column(String(50), comment="方向")
    position_code = Column(String(10), comment="摆位码")
    
    # 关联信息
    population = Column(String(50), comment="人群")
    disease = Column(String(100), comment="疾病")
    emergency_type = Column(String(20), comment="平急诊")
    
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 索引
    __table_args__ = (
        Index('idx_dr_part_code', 'part_code'),
        Index('idx_dr_position', 'position'),
    )


class InsuranceCode(BaseModel):
    """医保编码表"""
    __tablename__ = "insurance_codes"
    
    sequence_number = Column(String(20), comment="序号")
    collection_scope = Column(String(50), comment="归集口径")
    insurance_project_code = Column(String(50), unique=True, nullable=False, comment="医保项目码")
    insurance_mapping_code = Column(String(20), nullable=False, comment="医保映射码")
    insurance_project_name = Column(String(200), nullable=False, comment="医保项目名称")
    insurance_extension_code = Column(String(10), comment="医保扩展码")
    mutual_recognition_name = Column(String(200), comment="互认项目名称")
    remarks = Column(Text, comment="备注")
    
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 索引
    __table_args__ = (
        Index('idx_insurance_project_code', 'insurance_project_code'),
        Index('idx_insurance_mapping_code', 'insurance_mapping_code'),
    )


class MgProject(BaseModel):
    """MG项目表"""
    __tablename__ = "mg_projects"
    
    project_name = Column(String(200), nullable=False, comment="项目名称")
    project_code = Column(String(50), comment="项目编码")
    description = Column(Text, comment="项目描述")
    
    # 关联医保信息
    insurance_mapping_code = Column(String(20), comment="医保映射码")
    insurance_extension_code = Column(String(10), comment="医保扩展码")
    
    is_active = Column(Boolean, default=True, comment="是否启用")


class GeneratedProject(BaseModel):
    """生成的检查项目表"""
    __tablename__ = "generated_projects"
    
    # 基本信息
    modality = Column(String(10), nullable=False, comment="模态")
    project_name = Column(String(200), nullable=False, comment="项目名称")
    project_code = Column(String(50), unique=True, nullable=False, comment="项目编码")
    
    # 部位信息
    level1_code = Column(String(10), comment="一级编码")
    level1_name = Column(String(50), comment="一级部位")
    level2_code = Column(String(10), comment="二级编码")
    level2_name = Column(String(50), comment="二级部位")
    level3_code = Column(String(10), comment="三级编码")
    level3_name = Column(String(100), comment="三级部位")
    part_code = Column(String(20), comment="部位编码")
    
    # 医保信息
    insurance_mapping_code = Column(String(20), comment="医保映射编码")
    insurance_project_name = Column(String(200), comment="医保项目名称")
    insurance_extension_code = Column(String(10), comment="医保扩展码")
    insurance_project_code = Column(String(50), comment="医保项目码")
    
    # 扫描信息（CT/MR）
    scan_method = Column(String(100), comment="扫描方式")
    scan_code = Column(String(10), comment="扫描方式编码")
    
    # DR特有信息
    position = Column(String(100), comment="摆位")
    position_code = Column(String(10), comment="摆位码")
    
    # 编码信息
    population_code = Column(String(10), default='0', comment="人群编码")
    disease_code = Column(String(10), default='0', comment="疾病编码")
    emergency_code = Column(String(10), default='0', comment="平急诊编码")
    
    # 生成信息
    generation_batch_id = Column(String(50), comment="生成批次ID")
    generation_rule = Column(String(100), comment="生成规则")
    generation_time = Column(DateTime(timezone=True), server_default=func.now(), comment="生成时间")
    
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 索引
    __table_args__ = (
        Index('idx_generated_project_code', 'project_code'),
        Index('idx_generated_modality', 'modality'),
        Index('idx_generated_batch', 'generation_batch_id'),
        Index('idx_generated_time', 'generation_time'),
    )


class GenerationBatch(BaseModel):
    """项目生成批次表"""
    __tablename__ = "generation_batches"
    
    batch_id = Column(String(50), unique=True, nullable=False, comment="批次ID")
    batch_name = Column(String(100), nullable=False, comment="批次名称")
    modality = Column(String(10), comment="模态类型")
    generation_rule = Column(String(100), comment="生成规则")
    
    # 统计信息
    total_count = Column(Integer, default=0, comment="总数量")
    success_count = Column(Integer, default=0, comment="成功数量")
    failed_count = Column(Integer, default=0, comment="失败数量")
    
    # 状态信息
    status = Column(String(20), default='pending', comment="状态: pending, running, completed, failed")
    start_time = Column(DateTime(timezone=True), comment="开始时间")
    end_time = Column(DateTime(timezone=True), comment="结束时间")
    error_message = Column(Text, comment="错误信息")
    
    # 配置信息
    generation_config = Column(Text, comment="生成配置JSON")
    
    # 索引
    __table_args__ = (
        Index('idx_batch_id', 'batch_id'),
        Index('idx_batch_status', 'status'),
        Index('idx_batch_time', 'start_time'),
    )


class DataImportLog(BaseModel):
    """数据导入日志表"""
    __tablename__ = "data_import_logs"
    
    import_id = Column(String(50), unique=True, nullable=False, comment="导入ID")
    file_name = Column(String(200), nullable=False, comment="文件名")
    file_size = Column(Integer, comment="文件大小")
    sheet_name = Column(String(100), comment="Sheet名称")
    table_name = Column(String(100), comment="目标表名")
    
    # 统计信息
    total_rows = Column(Integer, default=0, comment="总行数")
    success_rows = Column(Integer, default=0, comment="成功行数")
    failed_rows = Column(Integer, default=0, comment="失败行数")
    
    # 状态信息
    status = Column(String(20), default='pending', comment="状态")
    start_time = Column(DateTime(timezone=True), comment="开始时间")
    end_time = Column(DateTime(timezone=True), comment="结束时间")
    error_message = Column(Text, comment="错误信息")
    
    # 详细信息
    import_config = Column(Text, comment="导入配置JSON")
    validation_errors = Column(Text, comment="验证错误JSON")
    
    # 索引
    __table_args__ = (
        Index('idx_import_id', 'import_id'),
        Index('idx_import_status', 'status'),
        Index('idx_import_time', 'start_time'),
    )
